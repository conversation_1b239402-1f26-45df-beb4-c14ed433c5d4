from django.conf import settings
from openai import OpenAI
import logging
import json
from feed.models import ApplicationCvText
from datetime import datetime
from django.utils import translation

logger = logging.getLogger(__name__)

def get_openai_client():
    api_key = settings.DEEPSEEK_API_KEY

    if not api_key:
        logger.error("Deepseek API key not found in environment variables")
        raise ValueError("Deepseek API key is not configured")

    return OpenAI(api_key=api_key, base_url="https://api.deepseek.com")

def get_today_date():
    """
    Get today's date in following format: "13 May 2025"

    Returns:
        str: Today's date as a string.
    """
    return datetime.now().strftime("%-d %B %Y")

def analyze_cv(cv_text, job_description):
    """
    Analyze a CV against a job description using Deepseek API

    Args:
        cv_text (str): The candidate's CV text
        job_description (str): The job description text

    Returns:
        dict: Analysis results in JSON format
    """
    print("Starting CV analysis")
    if not cv_text or not job_description:
        logger.error("CV text or job description is empty")
        return {
            "score": -1,
            "summary": "An error occurred during the analysis. Please try again later.",
            "highlights": ["Unable to process analysis due to a system error."],
            "drawbacks": ["Unable to process analysis due to a system error."]
        }
    try:
        client = get_openai_client()
        current_language = translation.get_language_info(translation.get_language())["name"]
        print("Current language for analysis:", current_language)
        # Create a prompt for the analysis
        prompt = f"""
        You are a professional CV/Resume Matching Assistant. Evaluate how well the candidate's CV matches the provided job description and generate a numerical score from 0-100.

        ## SCORING CRITERIA

        When determining scores, use these weighted factors:

        1. Core Skills Match (40%): Essential skills required for the position
        2. Experience Level (25%): Years of experience and seniority match
        3. Domain/Industry Knowledge (15%): Experience in the specific industry or domain
        4. Tool/Method Proficiency (10%): Proficiency with specific tools or technologies
        5. Additional Qualifications (10%): Certifications, education, complementary skills

        ## SKILL SUBSTANTIATION GUIDELINES

        When evaluating the Core Skills and Tool Proficiency factors above, use the following guidelines to weight different types of skills mentioned in the CV:

        1. Fully Substantiated Skills (100% weight)
        - Skills demonstrated through specific projects, responsibilities, or achievements
        - Example: "Developed REST APIs that processed 1M+ daily requests"

        2. Partially Substantiated Skills (50-60% weight)
        - Skills mentioned briefly in job descriptions without specific details
        - Example: "Used Python for data analysis"

        3. Unsubstantiated Skills (20-30% weight)
        - Skills only listed in a "Skills" section without supporting evidence in work history
        - Example: Just listing "JavaScript" in skills section


        ## SCORE INTERPRETATION

        Based on your analysis of the above factors and skill substantiation, assign a score using this scale:

        0: Completely unrelated background with no relevant skills or experience.

        1-20 (Very Poor Match): Minimal relevant experience or only tangential connection to the field, lacking most core requirements.

        21-40 (Poor Match): Some relevant background but missing key requirements or sufficient experience level, or primarily lists skills without substantiation.

        41-60 (Average Match): Meets about half of the key requirements, may be at a lower seniority level than required, or has relevant experience but in a different industry/domain.

        61-80 (Good Match): Meets most core requirements with appropriate experience level but may lack some secondary skills or has some skills without proper substantiation.

        81-99 (Excellent Match): Matches nearly all requirements with appropriate experience level and substantiates most skills with specific examples, with only minor gaps.

        100 (Perfect Match): Meets or exceeds all requirements with ideal seniority level and specialized expertise, with thorough substantiation of all key skills.


        ## ADDITIONAL CALCULATIONS

        1. Calculate the candidate's total professional experience by summing all relevant work experience durations from the CV. Express this in the format "X years, Y months". Today is {get_today_date()}.

        2. Identify the candidate's latest (most recent) employer and job title/role.

        3. Identify the candidate's highest/latest educational degree (e.g., "Bachelor of Science in Computer Science", "Master of Business Administration"). Include the field of study if available. If not explicitly stated, indicate "Not specified".

        4. Extract the candidate's location (city, state/province, country) as provided in the CV. If not explicitly stated, indicate "Not specified".

        ## Analyze Language

        {current_language}

        ## REQUIRED OUTPUT FORMAT

        Based on the scoring criteria, skill substantiation guidelines, and score interpretation above, provide your assessment in the following format in the language of Analyze Language above

        Return ONLY a valid JSON object with these exact fields in the same language as the job description. For example, if the job description is in English, return the JSON in English. If the job description is in Spanish, return the JSON in Spanish.
        {{
        "score": <integer from 0 to 100>,
        "summary": "<brief 2-3 sentence assessment of the overall match>",
        "highlights": [
            "<Key strength 1 of the candidate relative to the job>",
            "<Key strength 2 of the candidate relative to the job>",
            "<Key strength 3 of the candidate relative to the job>"
        ],
        "drawbacks": [
            "<Key gap 1 in the candidate's profile>",
            "<Key gap 2 in the candidate's profile>",
            "<Key gap 3 in the candidate's profile>"
        ],
        "total_experience": "<X years, Y months>",
        "latest_employer": "<Name of the most recent company>",
        "latest_role": "<Most recent job title>",
        "latest_degree": "<Highest/latest educational qualification with field>",
        "location": "<Candidate's current location or 'Not specified'>"
        }}


        ## JOB DESCRIPTION:
        {job_description}

        ## CANDIDATE CV:
        {cv_text}

        """

        # Call the Deepseek API
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are an AI assistant that analyzes CVs for job matching."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            response_format={"type": "json_object"}
        )

        # Extract and parse the JSON response
        result = json.loads(response.choices[0].message.content)

        # Validate the response has the expected structure
        expected_keys = ["score", "summary", "highlights", "drawbacks"]
        for key in expected_keys:
            if key not in result:
                logger.warning(f"Missing expected key '{key}' in API response")
                if key == "score":
                    result[key] = 50  # Default score
                elif key in ["highlights", "drawbacks"]:
                    result[key] = []  # Empty list for missing arrays
                else:
                    result[key] = ""  # Empty string for other fields

        return result
    except Exception as e:
        logger.error(f"Error analyzing CV: {str(e)}")
        # Return a fallback response in case of errors
        return {
            "score": -1,
            "summary": "An error occurred during the analysis. Please try again later.",
            "highlights": ["Unable to process analysis due to a system error."],
            "drawbacks": ["Unable to process analysis due to a system error."]
        }

def llm_session(job_summary):
    print(f"LLM session: title: {job_summary['roleTitle']}")
    try:
        client = get_openai_client()
        current_language = translation.get_language_info(translation.get_language())["name"]
        # Create a prompt for the job description
        prompt = f"""
        You are a professional job description writer. Write a job description for a {job_summary['roleTitle']} position with the following information:

        - Office Location: {job_summary['officeLocation']}
        - Work Schedule: {job_summary['workSchedule']}
        - Office Schedule: {job_summary['officeSchedule']}
        - Skills: {job_summary['skills']}
        - Salary Range: {job_summary['salaryMin']} to {job_summary['salaryMax']} in {job_summary['salaryCurrency']}
        - Benefits: {job_summary['benefits']}

        Return ONLY a valid JSON object with these exact fields in {current_language} language:
        {{
            "job_description": "<Full job description in rich text format in {current_language} language>"
        }}
        """

        # Call the Deepseek API
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are an AI assistant that writes professional job descriptions."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            response_format={"type": "json_object"}
        )

        # Extract and parse the JSON response
        result = json.loads(response.choices[0].message.content)

        return result
    except Exception as e:
        logger.error(f"Error creating job description: {str(e)}")
        # Return a fallback response in case of errors
        return {
            "job_description": "An error occurred while generating the job description. Please try again later."
        }

def extract_candidate_info_from_cv(cv_text):
    """
    Extract candidate information from CV text using AI.

    Args:
        cv_text (str): The candidate's CV text

    Returns:
        dict: Extracted candidate information
    """
    print("🤖 Starting AI candidate information extraction")
    print(f"CV text length: {len(cv_text) if cv_text else 0} characters")

    if not cv_text:
        print("❌ CV text is empty")
        logger.error("CV text is empty")
        return {
            "first_name": "Unknown",
            "last_name": "Unknown",
            "email": "",
            "phone": "",
            "location": "",
            "current_position": "Unknown",
            "current_employer": "Unknown",
            "total_experience_years": 0.0,
            "education_level": "Unknown"
        }

    try:
        print("🔑 Getting OpenAI client...")
        client = get_openai_client()
        print("✅ OpenAI client obtained")

        # Create a prompt for candidate information extraction
        print("📝 Creating AI prompt...")
        prompt = f"""
        You are a professional CV/Resume parser. Extract key candidate information from the provided CV text.

        ## EXTRACTION REQUIREMENTS

        Extract the following information from the CV:

        1. **Personal Information**:
           - First name and last name (separate fields)
           - Email address
           - Phone number
           - Current location (city, country)

        2. **Professional Information**:
           - Current/most recent job position/title
           - Current/most recent employer/company name
           - Total years of professional experience (as a decimal number, e.g., 3.5)
           - Highest education level (choose from: "High School", "Associate", "Bachelor", "Master", "PhD", "Unknown")

        ## EXTRACTION GUIDELINES

        - If information is not clearly stated, use appropriate defaults
        - For names: Extract from the beginning of the CV or contact section
        - For experience: Calculate total years from all professional positions
        - For education: Choose the highest degree mentioned
        - For current position/employer: Use the most recent job listed
        - Be conservative with experience calculation - only count clear professional roles

        ## REQUIRED OUTPUT FORMAT

        Return ONLY a valid JSON object with these exact fields:
        {{
            "first_name": "<First name or 'Unknown' if not found>",
            "last_name": "<Last name or 'Unknown' if not found>",
            "email": "<Email address or empty string if not found>",
            "phone": "<Phone number or empty string if not found>",
            "location": "<Current location or empty string if not found>",
            "current_position": "<Current job title or 'Unknown' if not found>",
            "current_employer": "<Current company name or 'Unknown' if not found>",
            "total_experience_years": <Total years as decimal number, 0.0 if not determinable>,
            "education_level": "<Highest education level or 'Unknown' if not found>"
        }}

        ## CANDIDATE CV:
        {cv_text}
        """

        print("🚀 Calling DeepSeek API...")
        # Call the Deepseek API
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are an AI assistant that extracts structured information from CVs."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,  # Lower temperature for more consistent extraction
            response_format={"type": "json_object"}
        )

        print("✅ DeepSeek API call successful")
        print(f"Raw API response: {response.choices[0].message.content}")

        # Extract and parse the JSON response
        print("📊 Parsing JSON response...")
        result = json.loads(response.choices[0].message.content)
        print(f"Parsed result: {result}")

        # Validate and set defaults for missing fields
        print("🔍 Validating and setting defaults...")
        defaults = {
            "first_name": "Unknown",
            "last_name": "Unknown",
            "email": "",
            "phone": "",
            "location": "",
            "current_position": "Unknown",
            "current_employer": "Unknown",
            "total_experience_years": 0.0,
            "education_level": "Unknown"
        }

        for key, default_value in defaults.items():
            if key not in result or result[key] is None:
                result[key] = default_value
                print(f"Set default for {key}: {default_value}")

        # Ensure total_experience_years is a float
        try:
            result["total_experience_years"] = float(result["total_experience_years"])
            print(f"Experience years: {result['total_experience_years']}")
        except (ValueError, TypeError):
            result["total_experience_years"] = 0.0
            print("Set default experience years: 0.0")

        print(f"✅ AI extraction successful: {result['first_name']} {result['last_name']}")
        logger.info(f"Successfully extracted candidate info: {result['first_name']} {result['last_name']}")
        return result

    except Exception as e:
        print(f"❌ AI extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
        logger.error(f"Error extracting candidate info: {str(e)}")
        # Return default values in case of errors
        return {
            "first_name": "Unknown",
            "last_name": "Unknown",
            "email": "",
            "phone": "",
            "location": "",
            "current_position": "Unknown",
            "current_employer": "Unknown",
            "total_experience_years": 0.0,
            "education_level": "Unknown"
        }