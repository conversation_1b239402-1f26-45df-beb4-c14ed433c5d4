from django.utils import translation
from django.conf import settings


class ForceLanguageMiddleware:
    """
    Custom middleware to ensure language is properly activated in Docker environments.
    This middleware checks for language preferences in session and cookies and 
    activates the language for each request.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Get language from various sources
        language = self.get_language_from_request(request)
        
        if language:
            print(f"[LANGUAGE-MIDDLEWARE] Activating language: {language}")
            translation.activate(language)
            # Set the language code in the request for templates
            request.LANGUAGE_CODE = language
        else:
            # Use default language
            print(f"[LANGUAGE-MIDDLEWARE] Using default language: {settings.LANGUAGE_CODE}")
            translation.activate(settings.LANGUAGE_CODE)
            request.LANGUAGE_CODE = settings.LANGUAGE_CODE

        response = self.get_response(request)
        
        # Deactivate language after request
        translation.deactivate()
        
        return response

    def get_language_from_request(self, request):
        """
        Get language preference from request in order of priority:
        1. Session
        2. Cookie
        3. Default language
        """
        # Check session first (highest priority)
        language = request.session.get('django_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session: {language}")
            return language
            
        language = request.session.get('_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session (_language): {language}")
            return language
            
        language = request.session.get(settings.LANGUAGE_COOKIE_NAME)
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in session (cookie name): {language}")
            return language

        # Check cookies
        language = request.COOKIES.get('django_language')
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in cookie: {language}")
            # Also set it in session for future requests
            request.session['django_language'] = language
            return language
            
        language = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME)
        if language and self.is_valid_language(language):
            print(f"[LANGUAGE-MIDDLEWARE] Found language in cookie (settings): {language}")
            # Also set it in session for future requests
            request.session['django_language'] = language
            return language

        # No language preference found
        print(f"[LANGUAGE-MIDDLEWARE] No language preference found")
        return None

    def is_valid_language(self, language):
        """Check if the language is in the list of available languages"""
        available_languages = [lang[0] for lang in settings.LANGUAGES]
        return language in available_languages
