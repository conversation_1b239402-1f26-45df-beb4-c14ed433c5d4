from django.utils import translation
from django.conf import settings


def language_context(request):
    """
    Context processor to add language information to all templates.
    This ensures language information is available even in Docker environments.
    """
    current_language = translation.get_language()
    
    # Get the current language name
    current_language_name = None
    for code, name in settings.LANGUAGES:
        if code == current_language:
            current_language_name = name
            break
    
    return {
        'CURRENT_LANGUAGE': current_language,
        'CURRENT_LANGUAGE_NAME': current_language_name,
        'AVAILABLE_LANGUAGES': settings.LANGUAGES,
    }
