from django import forms
from .models import Invitation

class InvitationForm(forms.ModelForm):
    ROLE_CHOICES = (
        ('Administrator', 'Administrator'),
        ('Recruiter', 'Recruiter'),
        ('Hiring Manager', 'Hiring Manager'),
        ('Interviewer', 'Interviewer'),
        ('Read Only', 'Read Only'),
    )
    
    LOCATION_CHOICES = (
        ('', 'Select a location'),  # Empty option
        ('New York', 'New York'),
        ('San Francisco', 'San Francisco'),
        ('London', 'London'),
        ('Remote', 'Remote'),
    )
    
    role = forms.ChoiceField(choices=ROLE_CHOICES)
    location = forms.ChoiceField(choices=LOCATION_CHOICES, required=False)
    
    class Meta:
        model = Invitation
        fields = ['first_name', 'last_name', 'email', 'role', 'location']