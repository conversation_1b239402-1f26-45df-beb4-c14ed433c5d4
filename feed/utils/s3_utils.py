import os
import boto3


class S3Client:
    def __init__(self, bucket_name):
        self.s3 = boto3.client(
            "s3",
            region_name=os.getenv("AWS_REGION"),
            endpoint_url=os.getenv("AWS_ENDPOINT_URL"),
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        )
        self.bucket_name = bucket_name

    def upload_file(self, file, key):
        """Upload a file to S3."""
        self.s3.upload_fileobj(file, self.bucket_name, key)

    def download_file(self, key):
        """Download a file from S3. if the file exists, then overwrite it."""
        try:
            response = self.s3.get_object(Bucket=self.bucket_name, Key=key)
            return response['Body'].read()
        except:
            raise FileNotFoundError(f"The file {key} does not exist in the bucket {self.bucket_name}.")

    def list_files(self, prefix=''):
        """List files in an S3 bucket."""
        response = self.s3.list_objects_v2(Bucket=self.bucket_name, Prefix=prefix)
        return [obj['Key'] for obj in response.get('Contents', [])]