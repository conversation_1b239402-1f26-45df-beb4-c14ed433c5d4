{% extends 'main.html' %}
{% block content %}
{% load static %}
{% load i18n %}

<div class="page-holder px-lg-4 px-xl-5 limited-width-content">
    <div class="container-fluid px-lg-5 px-xl-6 ">
        <section class="mb-lg-3 mt-5">
            <div class="row d-flex justify-content-center align-items-center">
                <div class="col-6">
                    <div class="card signin-field-card-border">
                        <div class="card-body">
                            <form action="{% url 'feed' %}" id="signin_form">
                                <div class="form-group mb-4">
                                    <label for="email_area">{% trans "Email" %}</label>
                                    <input type="email" class="form-control" id="email_area" aria-describedby="emailHelp" placeholder="{% trans '<EMAIL>' %}">
                                </div>
                                {% load bootstrap5%}
                                <div class="form-group">
                                    <label>{% trans "Password" %}</label>
                                    <div class="input-group mb-3" id="show_hide_password">
                                        <input type="password" class="form-control" aria-label="password" placeholder="">
                                        <div class="input-group-append">
                                          <span class="input-group-text">
                                            <a href=""><i class="fa fa-eye-slash text-black-50" aria-hidden="true"></i></a>
                                          </span>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    $(document).ready(function() {
                                    $("#show_hide_password a").on('click', function(event) {
                                        event.preventDefault();
                                        if($('#show_hide_password input').attr("type") == "text"){
                                            $('#show_hide_password input').attr('type', 'password');
                                            $('#show_hide_password i').addClass( "fa-eye-slash" );
                                            $('#show_hide_password i').removeClass( "fa-eye" );
                                        }else if($('#show_hide_password input').attr("type") == "password"){
                                            $('#show_hide_password input').attr('type', 'text');
                                            $('#show_hide_password i').removeClass( "fa-eye-slash" );
                                            $('#show_hide_password i').addClass( "fa-eye" );
                                        }
                                    });
                                });
                                </script>
                            </form>
                            <div class="row d-flex justify-content-center m-auto">
                                <button name="Button" id="signin_button" form="signin_form">{% trans "Signin" %}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

{%endblock%}