<!DOCTYPE html>
{% extends 'main.html' %}
{% load static %}
{% load i18n %}
{% block content %}
{% csrf_token %}

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Career Page Builder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
        }
        
        .careers-builder-container {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .builder-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .customization-panels {
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .panel-section {
            background-color: #fff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }
        
        .panel-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: #343a40;
            display: flex;
            align-items: center;
            font-size: 1.2rem;
        }
        
        .panel-title i {
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 1.3rem;
        }
        
        .color-input-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .color-input-group label {
            margin-right: 15px;
            min-width: 100px;
            font-weight: 500;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 12px 25px;
            font-size: 1.1rem;
            margin-top: 20px;
            width: 100%;
        }
        
        .preview-section {
            background-color: #fff;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }
        
        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .preview-controls .btn {
            margin-left: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            overflow: auto;
            border: 1px solid #e9ecef;
            min-height: 500px;
        }
        
        .preview-frame {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            min-height: 100%;
            overflow: auto;
            transition: all 0.3s ease;
        }
        
        .preview-frame.mobile {
            max-width: 375px;
            margin: 0 auto;
            height: 667px;
        }
        
        .preview-frame.tablet {
            max-width: 768px;
            margin: 0 auto;
            height: 1024px;
        }
        
        /* Preview styling */
        .preview-header-area {
            padding: 60px 20px;
            text-align: center;
            color: white;
            margin-bottom: 0;
        }
        
        .preview-header-area h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .preview-tagline {
            font-size: 1.25rem;
            max-width: 700px;
            margin: 0 auto;
            opacity: 0.9;
        }
        
        .preview-jobs-section {
            padding: 40px 20px;
            background: #f8f9fa;
        }
        
        .preview-jobs-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .preview-jobs-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .preview-jobs-header h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .preview-search-container {
            max-width: 600px;
            margin: 0 auto 30px;
            position: relative;
        }
        
        .preview-search-input {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1rem;
        }
        
        .preview-search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 50px;
        }
        
        .preview-jobs-grid {
            display: grid;
            gap: 20px;
        }
        
        .preview-job-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-left: 4px solid var(--primary-color);
        }
        
        .preview-job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .preview-job-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }
        
        .preview-job-type {
            background: rgba(108, 117, 125, 0.1);
            color: var(--secondary-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        .preview-job-location, .preview-job-salary {
            color: #666;
            margin: 0 0 8px 0;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
        }
        
        .preview-job-location i, .preview-job-salary i {
            margin-right: 8px;
            width: 20px;
        }
        
        .preview-job-description {
            color: #666;
            margin: 0 0 15px 0;
            line-height: 1.6;
        }
        
        .preview-apply-btn {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            border-radius: 6px;
            font-weight: 600;
            display: inline-block;
        }
        
        .form-control:focus, .form-control-color:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
        }
        
        @media (max-width: 768px) {
            .preview-controls .btn {
                width: 36px;
                height: 36px;
            }
            
            .preview-container {
                min-height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="careers-builder-container">
        <div class="builder-header">
            <h2><i class="fas fa-file-code me-2"></i>Career Page Builder</h2>
            <p class="text-muted">Create a complete careers page for your company</p>
        </div>
        
        <div class="customization-panels">
            <!-- Company Information Panel -->
            <div class="panel-section">
                <h3 class="panel-title">
                    <i class="fas fa-building"></i>
                    Company Information
                </h3>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="companyName" class="form-label fw-semibold">Company Name</label>
                        <input type="text" class="form-control" id="companyName" placeholder="Enter company name" value="Tech Innovations Inc.">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="companyTagline" class="form-label fw-semibold">Tagline</label>
                        <input type="text" class="form-control" id="companyTagline" placeholder="Enter company tagline" value="Build what matters. Grow your career. Shape the future.">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="logoUrl" class="form-label fw-semibold">Logo URL</label>
                        <input type="url" class="form-control" id="logoUrl" placeholder="https://example.com/logo.png" value="https://via.placeholder.com/200x60.png?text=Company+Logo">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="bannerUrl" class="form-label fw-semibold">Banner URL</label>
                        <input type="url" class="form-control" id="bannerUrl" placeholder="https://example.com/banner.jpg" value="https://via.placeholder.com/1200x400.png?text=Company+Banner">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="rssFeedUrl" class="form-label fw-semibold">RSS Feed URL</label>
                        <input type="url" class="form-control" id="rssFeedUrl" placeholder="https://example.com/feed.xml" value="{% static 'feed.xml' %}">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="enableSearch" class="form-label fw-semibold">Search Bar</label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="enableSearch" checked>
                            <label class="form-check-label" for="enableSearch">
                                Enable job search functionality
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Design Customization Panel -->
            <div class="panel-section">
                <h3 class="panel-title">
                    <i class="fas fa-palette"></i>
                    Design Customization
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="color-input-group">
                            <label for="primaryColor">Primary Color</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#007bff">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="color-input-group">
                            <label for="secondaryColor">Secondary Color</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Download Button -->
            <div class="panel-section">
                <button class="btn btn-download" id="downloadBtn">
                    <i class="fas fa-download me-2"></i>
                    Download Career Page
                </button>
                <small class="form-text text-muted mt-2 d-block text-center">Downloads a complete HTML careers page with RSS integration</small>
            </div>
        </div>

        <!-- Preview Section at Bottom -->
        <div class="preview-section">
            <div class="preview-header">
                <h3><i class="fas fa-eye me-2"></i>Live Preview</h3>
                <div class="preview-controls">
                    <button class="btn btn-outline-primary btn-sm" id="desktopBtn">
                        <i class="fas fa-desktop"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm" id="tabletBtn">
                        <i class="fas fa-tablet-alt"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm" id="mobileBtn">
                        <i class="fas fa-mobile-alt"></i>
                    </button>
                </div>
            </div>

            <div class="preview-container">
                <div class="preview-frame" id="previewFrame">
                    <!-- Preview will be generated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Extract RGB values from hex color
            function hexToRgb(hex) {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16)
                } : null;
            }
            
            // Update CSS variables with RGB values for transparency
            function updateCssVariables() {
                const primaryColor = document.getElementById('primaryColor').value || '#007bff';
                const secondaryColor = document.getElementById('secondaryColor').value || '#6c757d';
                
                // Set CSS variables
                document.documentElement.style.setProperty('--primary-color', primaryColor);
                document.documentElement.style.setProperty('--secondary-color', secondaryColor);
                
                // Set RGB values for transparency
                const primaryRgb = hexToRgb(primaryColor);
                if (primaryRgb) {
                    document.documentElement.style.setProperty('--primary-color-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
                }
                
                const secondaryRgb = hexToRgb(secondaryColor);
                if (secondaryRgb) {
                    document.documentElement.style.setProperty('--secondary-color-rgb', `${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b}`);
                }
            }
            
            // Set up event listeners for all inputs
            const inputIds = [
                'companyName', 'companyTagline', 'logoUrl', 'bannerUrl', 
                'rssFeedUrl', 'primaryColor', 'secondaryColor'
            ];
            
            inputIds.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });
            
            // Special handling for checkbox
            const enableSearch = document.getElementById('enableSearch');
            if (enableSearch) {
                enableSearch.addEventListener('change', updatePreview);
            }
            
            // Set up other event listeners
            document.getElementById('downloadBtn').addEventListener('click', downloadPage);
            document.getElementById('desktopBtn').addEventListener('click', () => togglePreviewMode('desktop'));
            document.getElementById('tabletBtn').addEventListener('click', () => togglePreviewMode('tablet'));
            document.getElementById('mobileBtn').addEventListener('click', () => togglePreviewMode('mobile'));
            
            // Initialize preview
            updateCssVariables();
            updatePreview();
        });

        // Update preview with current settings
        function updatePreview() {
            // Get current form values
            const companyName = document.getElementById('companyName').value || 'Your Company';
            const tagline = document.getElementById('companyTagline').value || 'Build what matters. Grow your career. Shape the future.';
            const logoUrl = document.getElementById('logoUrl').value;
            const bannerUrl = document.getElementById('bannerUrl').value;
            const rssFeedUrl = document.getElementById('rssFeedUrl').value;
            const primaryColor = document.getElementById('primaryColor').value || '#007bff';
            const secondaryColor = document.getElementById('secondaryColor').value || '#6c757d';
            const enableSearch = document.getElementById('enableSearch').checked;

            // Generate job cards HTML
            const jobCardsHTML = `
                <div class="preview-job-card">
                    <div class="preview-job-header">
                        <h3 class="preview-job-title">Senior UI Designer</h3>
                        <span class="preview-job-type">Full-time • Remote</span>
                    </div>
                    <p class="preview-job-location"><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                    <p class="preview-job-salary"><i class="fas fa-dollar-sign"></i> $90,000 - $120,000</p>
                    <p class="preview-job-description">We are seeking a highly skilled Senior UI Designer to join our dynamic team. You will create beautiful and functional user interfaces for our products.</p>
                    <a href="#" class="preview-apply-btn">Apply Now →</a>
                </div>
                <div class="text-center mt-3 text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Preview shows sample job. Actual jobs will load from RSS feed.
                </div>
            `;

            // Generate search bar HTML
            const searchBarHTML = enableSearch ? `
                <div class="preview-search-container">
                    <input type="text" class="preview-search-input" placeholder="Search jobs...">
                    <button class="preview-search-btn"><i class="fas fa-search"></i></button>
                </div>
            ` : '';

            // Generate header with optional banner and logo
            const headerStyle = bannerUrl ? 
                `background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('${bannerUrl}'); 
                 background-size: cover; 
                 background-position: center;` : 
                `background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});`;

            const logoHTML = logoUrl ? `<img src="${logoUrl}" alt="${companyName}" style="max-height: 60px; margin-bottom: 20px;">` : '';

            // Generate RSS feed indicator
            const rssIndicator = rssFeedUrl ? 
                `<div class="text-center mt-3">
                    <i class="fas fa-rss text-success me-2"></i> 
                    <span>RSS Feed Configured: ${rssFeedUrl}</span>
                </div>` : 
                `<div class="text-center mt-3">
                    <i class="fas fa-rss text-secondary me-2"></i> 
                    <span>No RSS Feed Configured</span>
                </div>`;

            // Generate complete HTML preview
            const previewHTML = `
                <div class="preview-header-area" style="${headerStyle}">
                    ${logoHTML}
                    <h1>${companyName}</h1>
                    <p class="preview-tagline">${tagline}</p>
                </div>

                <div class="preview-jobs-section">
                    <div class="preview-jobs-container">
                        <div class="preview-jobs-header">
                            <h2>Open Positions</h2>
                            <p>Join our team and help us build the future</p>
                            ${searchBarHTML}
                            ${rssIndicator}
                        </div>
                        <div class="preview-jobs-grid">
                            ${jobCardsHTML}
                        </div>
                    </div>
                </div>
            `;

            // Update the preview
            const previewFrame = document.getElementById('previewFrame');
            if (previewFrame) {
                previewFrame.innerHTML = previewHTML;
            }
        }

        // Toggle preview mode (desktop/tablet/mobile)
        function togglePreviewMode(mode) {
            const previewFrame = document.getElementById('previewFrame');
            if (!previewFrame) return;
            
            previewFrame.classList.remove('desktop', 'tablet', 'mobile');
            
            if (mode !== 'desktop') {
                previewFrame.classList.add(mode);
            }
        }

        // Download the generated page
        function downloadPage() {
            // Get current form values
            const companyName = document.getElementById('companyName').value || 'Your Company';
            const tagline = document.getElementById('companyTagline').value || 'Build what matters. Grow your career. Shape the future.';
            const logoUrl = document.getElementById('logoUrl').value;
            const bannerUrl = document.getElementById('bannerUrl').value;
            const rssFeedUrl = document.getElementById('rssFeedUrl').value;
            const primaryColor = document.getElementById('primaryColor').value || '#007bff';
            const secondaryColor = document.getElementById('secondaryColor').value || '#6c757d';
            const enableSearch = document.getElementById('enableSearch').checked;

            // Generate complete HTML file
            const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${companyName} - Careers</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; }
        
        .header {
            ${bannerUrl ? 
                `background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('${bannerUrl}'); 
                background-size: cover; 
                background-position: center;` : 
                `background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});`
            }
            color: white;
            padding: 80px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .header p {
            font-size: 1.25rem;
            max-width: 700px;
            margin: 0 auto;
            opacity: 0.9;
        }
        
        .header img {
            max-height: 80px;
            margin-bottom: 20px;
        }
        
        .jobs-section {
            padding: 60px 20px;
            background: #f8f9fa;
        }
        
        .jobs-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .jobs-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .jobs-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: ${primaryColor};
            margin-bottom: 15px;
        }
        
        .jobs-header p {
            font-size: 1.1rem;
            color: #666;
            max-width: 700px;
            margin: 0 auto 30px;
        }
        
        .search-container {
            max-width: 600px;
            margin: 0 auto 40px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 14px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: ${primaryColor};
            box-shadow: 0 0 0 0.25rem rgba(${hexToRgb(primaryColor).r}, ${hexToRgb(primaryColor).g}, ${hexToRgb(primaryColor).b}, 0.25);
        }
        
        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: ${primaryColor};
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .search-btn:hover {
            background: ${secondaryColor};
        }
        
        .jobs-grid {
            display: grid;
            gap: 24px;
        }
        
        .job-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border-left: 4px solid ${primaryColor};
            transition: all 0.3s ease;
        }
        
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .job-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: ${primaryColor};
            margin: 0;
        }
        
        .job-type {
            background: ${hexToRgb(secondaryColor).r}, ${hexToRgb(secondaryColor).g}, ${hexToRgb(secondaryColor).b}, 0.1);
            color: ${secondaryColor};
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        .job-location, .job-salary {
            color: #666;
            margin: 0 0 10px 0;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
        }
        
        .job-location i, .job-salary i {
            margin-right: 8px;
            width: 20px;
        }
        
        .job-description {
            color: #666;
            margin: 0 0 20px 0;
            line-height: 1.6;
            font-size: 0.95rem;
        }
        
        .apply-btn {
            background: ${primaryColor};
            color: white;
            text-decoration: none;
            padding: 10px 24px;
            border-radius: 6px;
            font-weight: 600;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .apply-btn:hover {
            background: ${secondaryColor};
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .rss-status {
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            max-width: 500px;
            margin: 20px auto 0;
        }
        
        .rss-status i {
            margin-right: 5px;
        }
        
        .rss-connected {
            color: #28a745;
        }
        
        .no-rss {
            color: #6c757d;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .jobs-header h2 {
                font-size: 1.75rem;
            }
            
            .job-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .job-type {
                margin-top: 10px;
            }
        }
        
        /* Helper function for RGB conversion */
        ${hexToRgb.toString()}
    </style>
</head>
<body>
    <header class="header">
        ${logoUrl ? `<img src="${logoUrl}" alt="${companyName}">` : ''}
        <h1>${companyName}</h1>
        <p>${tagline}</p>
        <div class="rss-status">
            <i class="fas fa-rss ${rssFeedUrl ? 'rss-connected' : 'no-rss'}"></i>
            <span>${rssFeedUrl ? 'Powered by Workloupe ATS' : 'RSS feed not configured'}</span>
        </div>
    </header>

    <section class="jobs-section">
        <div class="jobs-container">
            <div class="jobs-header">
                <h2>Open Positions</h2>
                <p>Join our team and help us build the future</p>
                ${enableSearch ? `
                <div class="search-container">
                    <input type="text" class="search-input" id="jobSearchInput" placeholder="Search jobs...">
                    <button class="search-btn" id="jobSearchBtn"><i class="fas fa-search"></i></button>
                </div>
                ` : ''}
            </div>
            <div class="jobs-grid" id="jobsContainer">
                <!-- Jobs will be loaded via RSS -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading job listings...</p>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        // Helper function to convert hex to RGB
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : {r: 0, g: 0, b: 0};
        }
        
        // Job search functionality
        function setupJobSearch() {
            const searchInput = document.getElementById('jobSearchInput');
            const searchBtn = document.getElementById('jobSearchBtn');
            
            if (searchInput && searchBtn) {
                const searchJobs = () => {
                    const searchTerm = searchInput.value.toLowerCase();
                    const jobCards = document.querySelectorAll('.job-card');
                    
                    jobCards.forEach(card => {
                        const title = card.querySelector('.job-title').textContent.toLowerCase();
                        const description = card.querySelector('.job-description').textContent.toLowerCase();
                        const location = card.querySelector('.job-location').textContent.toLowerCase();
                        
                        if (title.includes(searchTerm) || 
                            description.includes(searchTerm) || 
                            location.includes(searchTerm)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                };
                
                searchInput.addEventListener('input', searchJobs);
                searchBtn.addEventListener('click', searchJobs);
            }
        }
        
        // Load jobs from RSS feed
        function loadJobsFromRSS() {
            const rssFeedUrl = '${rssFeedUrl}';
            const jobsContainer = document.getElementById('jobsContainer');
            
            if (!rssFeedUrl) {
                jobsContainer.innerHTML = '<p class="text-center py-5">RSS feed not configured</p>';
                return;
            }
            
            fetch(rssFeedUrl)
                .then(response => response.text())
                .then(str => new window.DOMParser().parseFromString(str, "text/xml"))
                .then(data => {
                    const jobs = data.querySelectorAll("job");
                    let jobsHTML = '';
                    
                    jobs.forEach(jobEl => {
                        const title = jobEl.querySelector("title").textContent.trim();
                        const url = jobEl.querySelector("url").textContent.trim();
                        const company = jobEl.querySelector("company").textContent.trim();
                        const city = jobEl.querySelector("city").textContent.trim();
                        const country = jobEl.querySelector("country").textContent.trim();
                        const remote = jobEl.querySelector("remote").textContent.trim();
                        const description = jobEl.querySelector("description").textContent.trim();
                        const schedule = jobEl.querySelector("schedule").textContent.trim();
                        const salary = jobEl.querySelector("salary").textContent.trim();
                        
                        const location = remote === 'true' ? 'Remote' : \`\${city}, \${country}\`;
                        const jobType = schedule ? schedule : 'Full-time';
                        
                        jobsHTML += \`
                            <div class="job-card">
                                <div class="job-header">
                                    <h3 class="job-title">\${title}</h3>
                                    <span class="job-type">\${jobType}</span>
                                </div>
                                <p class="job-location"><i class="fas fa-map-marker-alt"></i> \${location}</p>
                                \${salary ? \`<p class="job-salary"><i class="fas fa-dollar-sign"></i> \${salary}</p>\` : ''}
                                <div class="job-description">\${description}</div>
                                <a href="\${url}" class="apply-btn">Apply Now →</a>
                            </div>
                        \`;
                    });
                    
                    jobsContainer.innerHTML = jobsHTML || '<p class="text-center py-5">No job openings at this time</p>';
                    setupJobSearch();
                })
                .catch(error => {
                    console.error('Error loading RSS feed:', error);
                    jobsContainer.innerHTML = '<p class="text-center py-5">Error loading job listings</p>';
                });
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', loadJobsFromRSS);
    </script>
</body>
</html>`;

            // Download the file
            const blob = new Blob([fullHTML], { type: 'text/html' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = companyName.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '_careers.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert('Career page downloaded successfully!');
        }
        
        // Helper function for hex to RGB conversion
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : {r: 0, g: 0, b: 0};
        }
    </script>
</body>
</html>
{% endblock %}