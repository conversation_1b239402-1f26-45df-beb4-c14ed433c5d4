{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - WordPress Configuration -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fab fa-wordpress me-2"></i>{% trans "WordPress Integration" %}</h4>
                    <p class="text-muted">{% trans "Configure your WordPress careers page" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- WordPress Setup Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-cog me-2"></i>
                            {% trans "WordPress Setup" %}
                        </h5>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Choose your preferred WordPress integration method" %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="wpMethod" class="form-label">{% trans "Integration Method" %}</label>
                            <select class="form-select" id="wpMethod" onchange="updateWPMethod()">
                                <option value="shortcode">{% trans "Shortcode (Recommended)" %}</option>
                                <option value="widget">{% trans "WordPress Widget" %}</option>
                                <option value="plugin">{% trans "Custom Plugin" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Company Branding Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Branding" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="Your Company">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Join our amazing team">
                        </div>
                    </div>

                    <!-- Design Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design Settings" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="wpThemeStyle" class="form-label">{% trans "WordPress Theme Style" %}</label>
                            <select class="form-select" id="wpThemeStyle">
                                <option value="inherit">{% trans "Inherit from Theme" %}</option>
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="responsiveDesign" checked>
                            <label class="form-check-label" for="responsiveDesign">
                                {% trans "Responsive Design" %}
                            </label>
                        </div>
                    </div>

                    <!-- Content Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-list me-2"></i>
                            {% trans "Content Settings" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="jobsPerPage" class="form-label">{% trans "Jobs Per Page" %}</label>
                            <select class="form-select" id="jobsPerPage">
                                <option value="5">5 {% trans "jobs" %}</option>
                                <option value="10" selected>10 {% trans "jobs" %}</option>
                                <option value="15">15 {% trans "jobs" %}</option>
                                <option value="all">{% trans "All jobs" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showFilters" checked>
                            <label class="form-check-label" for="showFilters">
                                {% trans "Show Job Filters" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showSearch" checked>
                            <label class="form-check-label" for="showSearch">
                                {% trans "Show Search Box" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showPagination" checked>
                            <label class="form-check-label" for="showPagination">
                                {% trans "Show Pagination" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Code Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateWPCode()">
                            <i class="fab fa-wordpress me-2"></i>
                            {% trans "Generate WordPress Code" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Preview and Instructions -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Preview & Instructions" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="showTab('preview')">
                            <i class="fas fa-eye"></i> {% trans "Preview" %}
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showTab('instructions')">
                            <i class="fas fa-book"></i> {% trans "Instructions" %}
                        </button>
                    </div>
                </div>

                <!-- Preview Tab -->
                <div id="previewTab" class="tab-content active">
                    <div class="preview-container">
                        <div class="preview-frame">
                            <div id="wpPreview" class="wp-preview">
                                <!-- WordPress preview will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions Tab -->
                <div id="instructionsTab" class="tab-content">
                    <div class="instructions-container">
                        <div class="instruction-section" id="shortcodeInstructions">
                            <h5><i class="fas fa-code me-2"></i>{% trans "Shortcode Integration" %}</h5>
                            <div class="alert alert-success">
                                <strong>{% trans "Recommended Method" %}</strong> - {% trans "Easy to use and works with any WordPress theme" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Copy the shortcode below" %}</li>
                                <li>{% trans "Go to your WordPress admin panel" %}</li>
                                <li>{% trans "Edit the page where you want to display jobs" %}</li>
                                <li>{% trans "Paste the shortcode in the content area" %}</li>
                                <li>{% trans "Save and publish the page" %}</li>
                            </ol>
                            <div class="code-block">
                                <code id="shortcodeCode">[workloupe_careers]</code>
                                <button class="btn btn-sm btn-outline-secondary" onclick="copyCode('shortcodeCode')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        <div class="instruction-section" id="widgetInstructions" style="display: none;">
                            <h5><i class="fas fa-puzzle-piece me-2"></i>{% trans "WordPress Widget" %}</h5>
                            <div class="alert alert-info">
                                {% trans "Perfect for sidebars and widget areas" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Go to Appearance > Widgets in your WordPress admin" %}</li>
                                <li>{% trans "Find the 'Workloupe Careers' widget" %}</li>
                                <li>{% trans "Drag it to your desired widget area" %}</li>
                                <li>{% trans "Configure the widget settings" %}</li>
                                <li>{% trans "Save the widget" %}</li>
                            </ol>
                        </div>

                        <div class="instruction-section" id="pluginInstructions" style="display: none;">
                            <h5><i class="fas fa-plug me-2"></i>{% trans "Custom Plugin" %}</h5>
                            <div class="alert alert-warning">
                                {% trans "Advanced option - requires technical knowledge" %}
                            </div>
                            <ol class="instruction-list">
                                <li>{% trans "Download the custom plugin file" %}</li>
                                <li>{% trans "Upload it to your WordPress plugins directory" %}</li>
                                <li>{% trans "Activate the plugin in WordPress admin" %}</li>
                                <li>{% trans "Configure the plugin settings" %}</li>
                                <li>{% trans "Use shortcodes or widgets as needed" %}</li>
                            </ol>
                            <button class="btn btn-success" onclick="downloadWPPlugin()">
                                <i class="fas fa-download me-2"></i>
                                {% trans "Download Plugin" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- WordPress Code Modal -->
<div class="modal fade" id="wpCodeModal" tabindex="-1" aria-labelledby="wpCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wpCodeModalLabel">
                    <i class="fab fa-wordpress me-2"></i>
                    {% trans "WordPress Integration Code" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Copy the code below and follow the integration instructions." %}
                </div>

                <div class="code-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label for="wpCode" class="form-label mb-0">{% trans "WordPress Code" %}</label>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyWPCode()">
                            <i class="fas fa-copy me-1"></i>
                            {% trans "Copy Code" %}
                        </button>
                    </div>
                    <textarea class="form-control code-textarea" id="wpCode" rows="15" readonly></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary" onclick="downloadWPFiles()">
                    <i class="fas fa-download me-2"></i>
                    {% trans "Download Files" %}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.careers-builder-container {
    height: 100vh;
    overflow: hidden;
}

.builder-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.sidebar-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.panel-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-title {
    color: #343a40;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-panel {
    background: #ffffff;
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-controls .btn {
    margin-left: 0.5rem;
}

.tab-content {
    height: calc(100vh - 120px);
    overflow: auto;
    display: none;
}

.tab-content.active {
    display: block;
}

.preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    height: 100%;
}

.preview-frame {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 400px;
    padding: 2rem;
}

.instructions-container {
    padding: 2rem;
    height: 100%;
}

.instruction-section {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.instruction-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.instruction-list li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.code-block {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-block code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    color: #e83e8c;
    background: none;
}

.code-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

@media (max-width: 992px) {
    .careers-builder-container {
        height: auto;
    }

    .builder-sidebar,
    .preview-panel {
        height: auto;
    }

    .tab-content {
        height: 500px;
    }
}
</style>

<script>
// WordPress integration functionality
let wpConfig = {
    method: 'shortcode',
    companyName: 'Your Company',
    tagline: 'Join our amazing team',
    primaryColor: '#343a40',
    themeStyle: 'inherit',
    responsiveDesign: true,
    jobsPerPage: 10,
    showFilters: true,
    showSearch: true,
    showPagination: true
};

// Initialize the WordPress builder
document.addEventListener('DOMContentLoaded', function() {
    initializeWPBuilder();
    updateWPPreview();
    showTab('preview');
});

function initializeWPBuilder() {
    // Add event listeners for all form controls
    document.getElementById('companyName').addEventListener('input', updateWPConfig);
    document.getElementById('companyTagline').addEventListener('input', updateWPConfig);
    document.getElementById('primaryColor').addEventListener('change', updateWPConfig);
    document.getElementById('wpThemeStyle').addEventListener('change', updateWPConfig);
    document.getElementById('responsiveDesign').addEventListener('change', updateWPConfig);
    document.getElementById('jobsPerPage').addEventListener('change', updateWPConfig);
    document.getElementById('showFilters').addEventListener('change', updateWPConfig);
    document.getElementById('showSearch').addEventListener('change', updateWPConfig);
    document.getElementById('showPagination').addEventListener('change', updateWPConfig);
}

function updateWPConfig() {
    wpConfig.companyName = document.getElementById('companyName').value;
    wpConfig.tagline = document.getElementById('companyTagline').value;
    wpConfig.primaryColor = document.getElementById('primaryColor').value;
    wpConfig.themeStyle = document.getElementById('wpThemeStyle').value;
    wpConfig.responsiveDesign = document.getElementById('responsiveDesign').checked;
    wpConfig.jobsPerPage = document.getElementById('jobsPerPage').value;
    wpConfig.showFilters = document.getElementById('showFilters').checked;
    wpConfig.showSearch = document.getElementById('showSearch').checked;
    wpConfig.showPagination = document.getElementById('showPagination').checked;

    updateWPPreview();
}

function updateWPMethod() {
    wpConfig.method = document.getElementById('wpMethod').value;

    // Show/hide instruction sections
    document.getElementById('shortcodeInstructions').style.display =
        wpConfig.method === 'shortcode' ? 'block' : 'none';
    document.getElementById('widgetInstructions').style.display =
        wpConfig.method === 'widget' ? 'block' : 'none';
    document.getElementById('pluginInstructions').style.display =
        wpConfig.method === 'plugin' ? 'block' : 'none';

    updateWPPreview();
}

function updateWPPreview() {
    renderWPPreview();
}

function renderWPPreview() {
    const previewContainer = document.getElementById('wpPreview');

    const searchSection = wpConfig.showSearch ? `
        <div style="margin-bottom: 20px;">
            <input type="text" placeholder="Search jobs..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
    ` : '';

    const filtersSection = wpConfig.showFilters ? `
        <div style="margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
            <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option>All Departments</option>
                <option>Engineering</option>
                <option>Marketing</option>
                <option>Sales</option>
            </select>
            <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option>All Locations</option>
                <option>Remote</option>
                <option>New York</option>
                <option>San Francisco</option>
            </select>
        </div>
    ` : '';

    const paginationSection = wpConfig.showPagination ? `
        <div style="margin-top: 30px; text-align: center;">
            <button style="padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; border-radius: 4px;">« Previous</button>
            <button style="padding: 8px 16px; margin: 0 5px; border: 1px solid ${wpConfig.primaryColor}; background: ${wpConfig.primaryColor}; color: white; border-radius: 4px;">1</button>
            <button style="padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; border-radius: 4px;">2</button>
            <button style="padding: 8px 16px; margin: 0 5px; border: 1px solid #ddd; background: white; border-radius: 4px;">Next »</button>
        </div>
    ` : '';

    previewContainer.innerHTML = `
        <div style="font-family: inherit; color: inherit;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: ${wpConfig.primaryColor}; margin-bottom: 10px;">Careers at ${wpConfig.companyName}</h2>
                <p style="color: #666; margin: 0;">${wpConfig.tagline}</p>
            </div>

            ${searchSection}
            ${filtersSection}

            <div style="space-y: 15px;">
                <!-- Sample job listings -->
                <div style="border: 1px solid #eee; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                    <h3 style="margin: 0 0 10px 0; color: ${wpConfig.primaryColor};">Senior Developer</h3>
                    <div style="color: #666; font-size: 0.9em; margin-bottom: 10px;">
                        📍 Remote • 💰 $80,000 - $120,000 • 📅 Posted 2 days ago
                    </div>
                    <p style="margin: 0 0 15px 0; line-height: 1.6;">We're looking for a senior developer to join our growing team...</p>
                    <button style="background: ${wpConfig.primaryColor}; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Apply Now</button>
                </div>

                <div style="border: 1px solid #eee; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                    <h3 style="margin: 0 0 10px 0; color: ${wpConfig.primaryColor};">Product Manager</h3>
                    <div style="color: #666; font-size: 0.9em; margin-bottom: 10px;">
                        📍 New York, NY • 💰 $90,000 - $130,000 • 📅 Posted 1 week ago
                    </div>
                    <p style="margin: 0 0 15px 0; line-height: 1.6;">Join our product team and help shape the future of our products...</p>
                    <button style="background: ${wpConfig.primaryColor}; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Apply Now</button>
                </div>
            </div>

            ${paginationSection}
        </div>
    `;
}

function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.add('active');

    // Update button states
    document.querySelectorAll('.preview-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-secondary');
    });
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-primary');
}

function generateWPCode() {
    const wpCode = generateWordPressCode();
    document.getElementById('wpCode').value = wpCode;

    const modal = new bootstrap.Modal(document.getElementById('wpCodeModal'));
    modal.show();
}

function generateWordPressCode() {
    if (wpConfig.method === 'shortcode') {
        return `// Add this to your theme's functions.php file

function workloupe_careers_shortcode($atts) {
    $atts = shortcode_atts(array(
        'company' => '${wpConfig.companyName}',
        'color' => '${wpConfig.primaryColor}',
        'jobs_per_page' => ${wpConfig.jobsPerPage},
        'show_filters' => '${wpConfig.showFilters}',
        'show_search' => '${wpConfig.showSearch}',
        'show_pagination' => '${wpConfig.showPagination}'
    ), $atts);

    ob_start();
    ?>
    <div id="workloupe-careers" style="font-family: inherit;">
        <style>
            .workloupe-careers-container {
                color: inherit;
            }
            .workloupe-job-card {
                border: 1px solid #eee;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
            .workloupe-job-title {
                color: <?php echo esc_attr($atts['color']); ?>;
                margin: 0 0 10px 0;
            }
            .workloupe-apply-btn {
                background: <?php echo esc_attr($atts['color']); ?>;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
        </style>

        <div class="workloupe-careers-container">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: <?php echo esc_attr($atts['color']); ?>;">
                    Careers at <?php echo esc_html($atts['company']); ?>
                </h2>
            </div>

            <div id="workloupe-jobs-list">
                <!-- Jobs will be loaded here via AJAX -->
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Load jobs via AJAX
            $.ajax({
                url: 'https://workloupe.com/api/jobs/{{ employer_name }}',
                method: 'GET',
                success: function(data) {
                    // Render jobs
                    var jobsHtml = '';
                    data.jobs.forEach(function(job) {
                        jobsHtml += '<div class="workloupe-job-card">';
                        jobsHtml += '<h3 class="workloupe-job-title">' + job.title + '</h3>';
                        jobsHtml += '<div style="color: #666; margin-bottom: 10px;">';
                        jobsHtml += '📍 ' + job.location + ' • 💰 ' + job.salary + ' • 📅 ' + job.date;
                        jobsHtml += '</div>';
                        jobsHtml += '<p>' + job.description + '</p>';
                        jobsHtml += '<a href="' + job.url + '" class="workloupe-apply-btn">Apply Now</a>';
                        jobsHtml += '</div>';
                    });
                    $('#workloupe-jobs-list').html(jobsHtml);
                }
            });
        });
        </script>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('workloupe_careers', 'workloupe_careers_shortcode');

// Usage: [workloupe_careers]`;
    } else if (wpConfig.method === 'widget') {
        return `// WordPress Widget Code
class Workloupe_Careers_Widget extends WP_Widget {
    function __construct() {
        parent::__construct(
            'workloupe_careers_widget',
            'Workloupe Careers',
            array('description' => 'Display job listings from Workloupe')
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Widget content
        echo '<div id="workloupe-careers-widget">';
        echo '<div style="color: ${wpConfig.primaryColor};">Loading jobs...</div>';
        echo '</div>';

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Careers';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Title:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>"
                   name="<?php echo $this->get_field_name('title'); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
}

function register_workloupe_careers_widget() {
    register_widget('Workloupe_Careers_Widget');
}
add_action('widgets_init', 'register_workloupe_careers_widget');`;
    } else {
        return `// Custom Plugin Code
<?php
/*
Plugin Name: Workloupe Careers Integration
Description: Integrate Workloupe job listings into your WordPress site
Version: 1.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WorkloupeCareersPlugin {
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('workloupe_careers', array($this, 'careers_shortcode'));
    }

    public function init() {
        // Plugin initialization
    }

    public function enqueue_scripts() {
        wp_enqueue_script('jquery');
        wp_enqueue_script('workloupe-careers', plugin_dir_url(__FILE__) . 'js/careers.js', array('jquery'), '1.0', true);
        wp_enqueue_style('workloupe-careers', plugin_dir_url(__FILE__) . 'css/careers.css', array(), '1.0');
    }

    public function careers_shortcode($atts) {
        // Shortcode implementation
        return workloupe_careers_shortcode($atts);
    }
}

new WorkloupeCareersPlugin();
?>`;
    }
}

function copyCode(elementId) {
    const codeElement = document.getElementById(elementId);
    const textArea = document.createElement('textarea');
    textArea.value = codeElement.textContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);

    // Show feedback
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    setTimeout(() => {
        button.innerHTML = originalText;
    }, 2000);
}

function copyWPCode() {
    const codeTextarea = document.getElementById('wpCode');
    codeTextarea.select();
    codeTextarea.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>{% trans "Copied!" %}';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        alert('{% trans "Failed to copy. Please copy manually." %}');
    }
}

function downloadWPFiles() {
    const wpCode = document.getElementById('wpCode').value;
    const blob = new Blob([wpCode], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workloupe-wordpress-${wpConfig.method}.php`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function downloadWPPlugin() {
    const pluginCode = generateWordPressCode();
    const blob = new Blob([pluginCode], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'workloupe-careers-plugin.php';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}