{% extends 'main.html' %}
{% load i18n %}
{% block content %}
<!-- Font Awesome CDN Link -->
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
  integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>

<div class="feed-dashboard">
  <!-- Header Section -->
  <div class="dashboard-header">
    <h1>{% trans "Dashboard" %}</h1>
    <div class="date-display">
      <i class="fas fa-calendar-alt"></i>
      <span id="current-date"> {% trans "Loading..." %} </span>
    </div>
  </div>

  <!-- Main Content - Reorganized into 3 Rows -->
  <div class="dashboard-container">
    <!-- Row 1: Calendar (2/3) and Activity Feed (1/3) -->
    <div class="dashboard-row row-first">
      <!-- Calendar Section (2/3) -->
      <div class="dashboard-column col-two-thirds">
        <div class="dashboard-card calendar-card">
          <div class="card-header">
            <h2><i class="fas fa-calendar"></i> {% trans "Calendar" %}</h2>
            <div class="card-actions">
              <button id="day-view-btn" class="view-btn">{% trans "Day" %}</button>
              <button id="week-view-btn" class="view-btn">{% trans "Week" %}</button>
              <button id="month-view-btn" class="view-btn active">{% trans "Month" %}</button>
            </div>
          </div>

          <div class="calendar-container">
            <div class="calendar-header">
              <div class="calendar-navigation">
                <button id="prev-btn" class="nav-btn">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <h2 id="calendar-title"> {% trans "Loading..." %}</h2>
                <button id="next-btn" class="nav-btn">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
              <button id="today-btn" class="today-btn">{% trans "Today" %}</button>
            </div>

            <!-- Hidden explanation about event indicators for monthly view -->
            <div id="month-view-info" class="view-info">
              <p>{% trans "Click on a day with colored dots to view events" %}.</p>
            </div>

            <div id="month-view" class="calendar-view active">
              <div class="weekdays">
                <div>{% trans "Mon" %}</div>
                <div>{% trans "Tue" %}</div>
                <div>{% trans "Wed" %}</div>
                <div>{% trans "Thu" %}</div>
                <div>{% trans "Fri" %}</div>
                <div>{% trans "Sat" %}</div>
                <div>{% trans "Sun" %}</div>
              </div>
              <div id="month-grid" class="month-grid"></div>
            </div>

            <div id="week-view" class="calendar-view">
              <div class="weekdays">
                <div>{% trans "Mon" %}</div>
                <div>{% trans "Tue" %}</div>
                <div>{% trans "Wed" %}</div>
                <div>{% trans "Thu" %}</div>
                <div>{% trans "Fri" %}</div>
                <div>{% trans "Sat" %}</div>
                <div>{% trans "Sun" %}</div>
              </div>
              <div id="week-grid" class="week-grid"></div>
            </div>

            <div id="day-view" class="calendar-view">
              <div id="day-grid" class="day-grid"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Feed Section (1/3) -->
      <div class="dashboard-column col-one-third">
        <div class="dashboard-card activity-card">
          <div class="card-header">
            <h2><i class="fas fa-bell"></i> {% trans "Activity Feed" %}</h2>
          </div>

          <div class="activity-container">
            <div
              class="activity-list"
              style="
                max-height: 600px;
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: var(--primary-color) transparent;
              "
            >
              {% for activity in activity_feed %}
              <div class="activity-item {{ activity.activity_type }}-activity">
                <div class="activity-icon">
                  {% if activity.activity_type == 'application' %}
                  <i class="fas fa-file-alt"></i>
                  {% elif activity.activity_type == 'state_change' %}
                  <i class="fas fa-exchange-alt"></i>
                  {% elif activity.activity_type == 'vacancy' %}
                  <i class="fas fa-briefcase"></i>
                  {% elif activity.activity_type == 'comment' %}
                  <i class="fas fa-comment-dots"></i>
                  {% else %}
                  <i class="fas fa-info-circle"></i>
                  {% endif %}
                </div>
                <div class="activity-content">
                  <p class="activity-text">
                    {{ activity.formatted_message|safe }}
                  </p>
                  <p class="activity-time">{{ activity.age_msg }}</p>
                </div>
              </div>
              {% endfor %}
            </div>
            <style></style>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 2: Hot Jobs (Full Width) -->
    <div class="dashboard-row row-second">
      <div class="dashboard-column col-full">
        <div class="dashboard-card hot-jobs-card">
          <div class="card-header">
            <h2><i class="fas fa-fire"></i> {% trans "Hot" %} {% trans "Jobs" %} </h2>
            <div class="card-actions">
              <a href="{% url 'jobs' %}" class="view-btn active"
                > {% trans "View All Jobs" %}</a
              >
            </div>
          </div>

          <div class="hot-jobs-container">
            {% for job in hot_jobs %}
            <div class="hot-job-item">
              <div class="hot-job-info">
                <h3>{{ job.title }}</h3>
                <div class="job-meta">
                  <span class="job-location">
                    <i class="fas fa-map-marker-alt"></i> {{ job.location }}
                  </span>
                  <span class="applicants-count">
                    <i class="fas fa-users"></i> {{ job.total_applicants }}
                    {% trans "Applicants" %}
                  </span>
                </div>
                <div class="hot-job-chart">
                  <canvas id="job-chart-{{ forloop.counter }}"></canvas>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Row 3: Monthly Overview (Full Width) -->
    <div class="dashboard-row row-third">
      <div class="dashboard-column col-full">
        <div class="dashboard-card overview-card">
          <div class="card-header">
            <h2>
              <i class="fas fa-chart-line"></i> {% trans "Monthly Applicant Overview" %}
            </h2>
          </div>

          <div class="overview-container">
            <div class="overview-stats">
              {% for info in overview_sums %}

              <div class="overview-stat">
                <div
                  class="stat-icon {% if info.state_name == 'Rejected' %}interviews-icon{% elif info.state_name == 'New' %}applications-icon{% elif info.state_name == 'In-Review' %}offers-icon{% else %}hires-icon{% endif %}"
                >
                  <i
                    class="fas {% if info.state_name == 'Rejected' %}fa-circle-minus{% elif info.state_name == 'New' %}fa-user-plus{% elif info.state_name == 'In-Review' %}fa-calendar-day{% else %}fa-flag-checkered{% endif %}"
                  ></i>
                </div>
                <div class="stat-info">
                  <p class="stat-label">{{ info.state_name }} {% trans "Applicants" %}</p>
                  <p class="stat-value">{{info.count}}</p>
                </div>
              </div>

              {% endfor %}
            </div>

            <div class="overview-chart">
              <canvas id="overview-chart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Event Modal -->
<div class="modal" id="events-modal">
  <div class="modal-content" style="max-width: 1000px; width: 90%; margin: 3% auto;">
    <div class="modal-header">
      <h2 id="modal-title"> {% trans "Events for Date" %}</h2>
      <span class="close-modal">&times;</span>
    </div>
    <div class="modal-body">
      <div id="modal-events" class="modal-events">
        <!-- Example of how each event will look with action buttons -->
        <div class="modal-event color-1">
          <div class="modal-event-time">13:30 - 14:30 </div>
          <div class="modal-event-title">Online Meeting</div>
          <div class="attendees-list">With: John Doe</div>
          <div class="modal-event-actions">
            <button class="edit-event-btn"><i class="fas fa-edit"></i></button>
            <button class="delete-event-btn">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Add New Event Button -->
      <div class="add-event-container">
        <button id="add-event-btn" class="add-event-btn">
          + {% trans "Add New Event" %}
        </button>
      </div>

      <!-- Event Creation Form (initially hidden) -->
      <div
        id="event-form-container"
        class="event-form-container"
        style="display: none"
      >
        <h3> {% trans "Create New Event" %} </h3>
        <form id="new-event-form">
          <div class="form-group">
            <label for="event-title"> {% trans "Event Title" %}</label>
            <input
              type="text"
              id="event-title"
              placeholder="{% trans 'Enter event title' %}"
              required
            />
          </div>

          <div class="form-group">
            <label for="event-type" class="form-label">{% trans "Event Type" %}</label>
            <select
              id="event-type"
              class="form-select form-select-lg mb-3"
              required
            >
              <option value=""> {% trans "Select an event type" %}</option>
              {% for kind in appointment_form_data.appointment_kinds %}
              <option value="{{ kind }}">{{ kind }}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group">
            <label for="recruiters" class="form-label">{% trans "Recruiters" %}</label>
            <select
              id="recruiters-dropdown"
              class="form-select form-select-lg mb-3"
            >
              <option value="">{% trans "Select one or many recruiters" %}</option>
              {% for recruiter in appointment_form_data.possible_interviewers %}
              <option value="{{ recruiter }}">{{ recruiter }}</option>
              {% endfor %}
            </select>
            <div id="selected-recruiters" class="selected-recruiters"></div>
            <input type="hidden" id="recruiters-input" name="recruiters" />
          </div>

          <script>
            document.addEventListener("DOMContentLoaded", function () {
              const dropdown = document.getElementById("recruiters-dropdown");
              const selectedRecruiters = document.getElementById(
                "selected-recruiters"
              );
              const recruitersInput =
                document.getElementById("recruiters-input");
              const selectedNames = new Set();

              dropdown.addEventListener("change", function () {
                const selectedName = this.value;
                if (selectedName && !selectedNames.has(selectedName)) {
                  selectedNames.add(selectedName);
                  updateSelectedRecruiters();
                }
                this.selectedIndex = 0;
              });

              function updateSelectedRecruiters() {
                selectedRecruiters.innerHTML = "";
                recruitersInput.value = Array.from(selectedNames).join(", ");

                selectedNames.forEach((name) => {
                  const nameSpan = document.createElement("span");
                  nameSpan.textContent = name;
                  nameSpan.className = "selected-name";

                  const removeButton = document.createElement("button");
                  removeButton.textContent = "x";
                  removeButton.className = "remove-name";
                  removeButton.onclick = function () {
                    selectedNames.delete(name);
                    updateSelectedRecruiters();
                  };

                  nameSpan.appendChild(removeButton);
                  selectedRecruiters.appendChild(nameSpan);
                });
              }
            });
          </script>

          <div class="form-group" id="vacancy-group">
            <label for="vacancy" class="form-label">{% trans "Position" %}</label>
            <select
              id="vacancy"
              class="form-select form-select-lg mb-3"
              name="vacancy"
              hx-get="{% url 'get_candidates_for_vacancy' %}"
              hx-target="#candidates-dropdown"
              hx-trigger="change"
            >
              <option value="">{% trans "Select the relevant position" %}</option>
              {% if appointment_form_data.possible_vacancies %}
              {% for vac in appointment_form_data.possible_vacancies %}
              <option value="{{ vac.vacancy_id }}">
                {{ vac.vacancy_title }}
              </option>
              {% endfor %} {% else %}
              <option value="" disabled>{% trans "No vacancies available" %}</option>
              {% endif %}
            </select>
          </div>

          <div class="form-group">
            <label for="candidates" class="form-label">{% trans "Candidate" %}</label>
            <select
              id="candidates-dropdown"
              class="form-select form-select-lg mb-3"
              name="candidates"
            >
              <option value="" selected>
                {% trans "Pick a Vacancy to see candidates" %}
              </option>
            </select>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="event-start-time">{% trans "Start Time" %}</label>
              <input type="time" id="event-start-time" required />
            </div>

            <div class="form-group">
              <label for="event-end-time">{% trans "End Time" %}</label>
              <input type="time" id="event-end-time" required />
            </div>
          </div>

          <div class="form-group">
            <label for="event-link">{% trans "Meeting Link" %}</label>
            <div class="input-group" style="display: flex; gap: 8px;">
              <input
                type="text"
                id="event-link"
                placeholder="{% trans 'Enter meeting link' %}"
                style="flex: 1;"
              />
              <button 
                type="button" 
                id="generate-jitsi-link" 
                class="btn btn-secondary"
                style="white-space: nowrap;"
              >
                {% trans "Generate Mirotalk Link" %}
              </button>
            </div>
          </div>

          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="inform_invitees"
              name="inform_invitees"
              value="true"
              checked
            />
            <label class="form-check-label" for="inform_invitees">
              {% trans "Inform invitees by E-mail" %}
            </label>
          </div>

          <div class="form-group">
            <label for="event-color">{% trans "Color" %}</label>
            <select id="event-color">
              <option value="1">{% trans "Blue" %}</option>
              <option value="2">{% trans "Light Blue" %}</option>
              <option value="3">{% trans "Purple" %}</option>
              <option value="4">{% trans "Pink" %}</option>
            </select>
          </div>

          <div class="form-buttons">
            <button type="button" id="cancel-event-btn" class="cancel-btn">
              {% trans "Cancel" %}
            </button>
            <button type="submit" id="save-event-btn" class="save-btn">
              {% trans "Save Event" %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- CSS Styles -->
<style>
  /* Root Variables */
  :root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --mid-gray: #e0e0e0;
    --dark-gray: #888;
    --event-color-1: #4cc9f0;
    --event-color-2: #4895ef;
    --event-color-3:rgb(89, 0, 190);
    --event-color-4:rgb(255, 51, 143);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
  }

  /* Main Dashboard Styles */
  .feed-dashboard {
    padding: 2rem;
    background-color: #f8f9fa;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    color: #333;
    min-height: 100vh;
  }

  /* Header Styles */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #1a202c;
  }

  .date-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    font-size: 0.875rem;
  }

  /* Dashboard Container and Rows Layout */
  .dashboard-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .dashboard-row {
    display: flex;
    gap: 1.5rem;
  }

  /* Column Styles */
  .dashboard-column {
    display: flex;
    flex-direction: column;
  }

  .col-two-thirds {
    flex: 2;
  }

  .col-one-third {
    flex: 1;
  }

  .col-full {
    flex: 1;
    width: 100%;
  }

  /* Card Styles */
  .dashboard-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    height: 100%;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .card-header h2 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .card-header h2 i {
    color: #4f46e5;
  }

  .card-actions {
    display: flex;
    gap: 0.75rem;
  }

  /* Calendar View Info */
  .view-info {
    display: none;
    text-align: center;
    color: var(--dark-gray);
    font-size: 0.9rem;
    margin: 0.5rem 0;
    padding: 0.5rem;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
  }

  /* Calendar Container Styles */
  .calendar-container {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    color: var(--text-color);
    height: 100%;
  }

  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--mid-gray);
    flex-wrap: wrap;
  }

  .calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  #calendar-title {
    margin: 0;
    font-size: 1.2rem;
    min-width: 150px;
    text-align: center;
  }

  .calendar-view-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
  }

  .nav-btn,
  .view-btn,
  .today-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
  }

  .nav-btn:hover,
  .view-btn:hover,
  .today-btn:hover {
    background-color: var(--light-gray);
  }

  .view-selector {
    display: flex;
    gap: 0.5rem;
  }

  .view-btn.active {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;
  }

  .today-btn {
    background-color: #3f37c9;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
  }

  /* Calendar View Layouts */
  .calendar-view {
    display: none;
    padding: 0.5rem;
  }

  .calendar-view.active {
    display: block;
  }

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid var(--mid-gray);
    padding-bottom: 0.5rem;
  }

  .month-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.3rem;
    height: 500px;
  }

  .week-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.3rem;
    height: 500px;
  }

  .day-grid {
    display: grid;
    grid-template-rows: repeat(24, minmax(40px, auto));
    gap: 0.25rem;
    height: 500px;
    overflow-y: auto;
  }

  /* Calendar Day Styles */
  .month-day {
    border: 1px solid var(--mid-gray);
    border-radius: 4px;
    padding: 0.3rem;
    height: 70px;
    background-color: white;
    display: flex;
    flex-direction: column;
    cursor: pointer;
  }

  .month-day.inactive {
    background-color: var(--light-gray);
    color: var(--dark-gray);
  }

  .month-day.today {
    border: 2px solid #4f46e5;
  }

  .day-number {
    font-weight: bold;
    margin-bottom: 0.3rem;
  }

  .week-day {
    border: 1px solid var(--mid-gray);
    border-radius: 4px;
    padding: 0.3rem;
    background-color: white;
    overflow-y: auto;
  }

  .week-day.today {
    border: 2px solid #4f46e5;
  }

  .day-hour {
    border-bottom: 1px solid var(--mid-gray);
    padding: 0.25rem;
    font-size: 0.85rem;
    display: flex;
    align-items: flex-start;
    min-height: 40px;
  }

  .hour-label {
    width: 50px;
    text-align: right;
    padding-right: 10px;
    color: var(--dark-gray);
  }

  /* Event Styles */
  .event {
    margin-top: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    color: white;
  }

  .event-indicators {
    display: flex;
    gap: 0.25rem;
    margin-top: auto;
    padding-top: 0.25rem;
  }

  .event-indicator {
    width: 0.6rem;
    height: 0.6rem;
    border-radius: 50%;
  }

  .event.color-1,
  .event-indicator.color-1 {
    background-color: var(--event-color-1);
  }
  .event.color-2,
  .event-indicator.color-2 {
    background-color: var(--event-color-2);
  }
  .event.color-3,
  .event-indicator.color-3 {
    background-color: var(--event-color-3);
  }
  .event.color-4,
  .event-indicator.color-4 {
    background-color: var(--event-color-4);
  }

  /* Modal styles for events calendar */
  #events-modal.modal {
    z-index: 1100;
  }

  #events-modal .modal-content {
    background-color: white;
    margin: 5% auto;
    max-width: 500px;
    width: 90%;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s;
  }

  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  #events-modal .modal-header {
    padding: 15px;
    border-bottom: 1px solid var(--mid-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  #events-modal .modal-header h2 {
    margin: 0;
    font-size: 18px;
  }

  .close-modal {
    font-size: 24px;
    cursor: pointer;
    color: #999;
  }

  .close-modal:hover {
    color: #333;
  }

  #events-modal .modal-body {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
  }

  /* Event styling in modal */
  .modal-events {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .modal-event {
    padding: 12px;
    border-radius: 6px;
    color: white;
  }

  .modal-event.color-1 {
    background-color: var(--event-color-1);
  }
  .modal-event.color-2 {
    background-color: var(--event-color-2);
  }
  .modal-event.color-3 {
    background-color: var(--event-color-3);
  }
  .modal-event.color-4 {
    background-color: var(--event-color-4);
  }

  .modal-event-time {
    font-weight: bold;
    margin-bottom: 6px;
  }

  .modal-event-title {
    font-size: 14px;
  }

  /* Calendar event edit and delete buttons */
  .edit-event-btn,
  .delete-event-btn {
    background: none;
    border: none;
    padding: 3px;
    margin-left: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .edit-event-btn:hover,
  .delete-event-btn:hover {
    opacity: 1;
  }

  .edit-event-btn i {
    color: #ffffff;
  }

  .delete-event-btn i {
    color: #ffffff;
  }

  /* Modal event actions container */
  .modal-event-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .modal-event .edit-event-btn,
  .modal-event .delete-event-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 5px 8px;
    margin-left: 8px;
  }

  /* Event styling in calendar views */
  .week-day .event,
  .day-hour .event {
    position: relative;
    padding-right: 60px; /* Make room for buttons */
  }

  .week-day .event .edit-event-btn,
  .week-day .event .delete-event-btn,
  .day-hour .event .edit-event-btn,
  .day-hour .event .delete-event-btn {
    position: absolute;
    top: 2px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 2px 4px;
  }

  .week-day .event .edit-event-btn,
  .day-hour .event .edit-event-btn {
    right: 30px;
  }

  .week-day .event .delete-event-btn,
  .day-hour .event .delete-event-btn {
    right: 2px;
  }

  /* Saving button state */
  #save-event-btn:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
  }

  /* Add hover effect to days in month view */
  .month-day {
    transition: background-color 0.2s;
  }

  .month-day:not(.inactive):hover {
    background-color: #f8f9fa;
    cursor: pointer;
  }

  /* Improve form layout */
  .form-group input:focus,
  .form-group select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
  }

  /* Loading state for buttons */
  .loading {
    position: relative;
    color: transparent !important;
  }

  .loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    animation: button-loading-spinner 0.6s linear infinite;
  }

  @keyframes button-loading-spinner {
    from {
      transform: rotate(0turn);
    }
    to {
      transform: rotate(1turn);
    }
  }

  /* Add Event button styles */
  .add-event-container {
    margin-top: 20px;
    text-align: center;
  }

  .add-event-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .add-event-btn:hover {
    background-color: var(--secondary-color);
  }

  /* Event form styles */
  .event-form-container {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--mid-gray);
  }

  .event-form-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
  }

  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--mid-gray);
    border-radius: 4px;
    font-size: 14px;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
  }

  .form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .cancel-btn,
  .save-btn {
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .cancel-btn {
    background-color: white;
    color: var(--dark-gray);
    border: 1px solid var(--mid-gray);
  }

  .cancel-btn:hover {
    background-color: var(--light-gray);
  }

  .save-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }

  .save-btn:hover {
    background-color: var(--secondary-color);
  }

  .attendees-list {
    margin-top: 8px;
    font-size: 12px;
    color: white;
  }

  /* Hot Jobs Section */
  .hot-jobs-container {
    padding: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .hot-job-item {
    flex: 1;
    min-width: 300px;
    display: flex;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8fafc;
  }

  .hot-job-info {
    flex: 1;
  }

  .hot-job-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
  }

  .job-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.75rem;
    color: #64748b;
  }

  .job-department {
    padding: 0.25rem 0.5rem;
    background-color: #eff6ff;
    color: #3b82f6;
    border-radius: 9999px;
    font-weight: 500;
  }

  .hot-job-chart {
    width: 100%; /* Expand to full width */
    height: auto; /* Maintain aspect ratio */
    max-height: 120px; /* Limit height */
  }

  /* Restrict hot job charts to fit within their containers */
  .hot-job-chart canvas {
    max-width: 100%;
    height: auto;
  }

  /* Activity Feed Section */
  .activity-container {
    padding: 1.5rem;
  }

  .activity-filter {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.75rem;
    outline: none;
  }

  .activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
  }

  .application-activity .activity-icon {
    background-color: #4f46e5;
  }

  .comment-activity .activity-icon {
    background-color: rgb(218, 70, 229);
  }

  .vacancy-activity .activity-icon {
    background-color: #0ea5e9;
  }

  .reject-activity .activity-icon {
    background-color: #dc2626;
  }

  .state_change-activity .activity-icon {
    background-color: #ca8a04;
  }

  .hire-activity .activity-icon {
    background-color: #16a34a;
  }

  .activity-content {
    flex: 1;
  }

  .activity-text {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .activity-time {
    margin: 0;
    font-size: 0.75rem;
    color: #64748b;
  }

  .activity-actions {
    align-self: flex-start;
  }

  .activity-action-btn {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    border-radius: 0.25rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
  }

  .activity-action-btn:hover {
    background-color: #f1f5f9;
    color: #1a202c;
  }

  .load-more-btn {
    display: block;
    width: 100%;
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4f46e5;
    cursor: pointer;
    transition: all 0.2s;
  }

  .load-more-btn:hover {
    background-color: #f8fafc;
    border-color: #cbd5e1;
  }

  /* Weekly Overview Section */
  .overview-container {
    padding: 1.5rem;
  }

  .overview-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .overview-stat {
    flex: 1;
    min-width: 140px;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8fafc;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .applications-icon {
    background-color: #4f46e5;
  }

  .interviews-icon {
    background-color: rgb(178, 8, 8);
  }

  .offers-icon {
    background-color: #ca8a04;
  }

  .hires-icon {
    background-color: rgb(2, 14, 6);
  }

  .stat-info {
    flex: 1;
  }

  .stat-label {
    font-size: 0.85rem;
    color: #64748b;
    margin: 0 0 0.25rem 0;
  }

  .stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0 0 0.25rem 0;
  }

  .positive {
    color: #16a34a;
  }

  .negative {
    color: #dc2626;
  }

  .overview-chart {
    height: 200px;
  }

  /* Common Button Styles */
  .view-all-btn {
    background: none;
    border: none;
    color: #4f46e5;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
  }

  .view-all-btn:hover {
    text-decoration: underline;
  }

  /* Modal Styles */
  .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    align-items: center;
    justify-content: center;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .row-first {
      flex-direction: column;
    }

    .col-two-thirds,
    .col-one-third {
      width: 100%;
    }
  }

  @media (max-width: 992px) {
    .feed-dashboard {
      padding: 1.5rem;
    }

    .hot-jobs-container {
      flex-direction: column;
    }

    .hot-job-item {
      width: 100%;
    }
  }

  @media (max-width: 768px) {
    .calendar-header {
      flex-direction: column;
      gap: 0.5rem;
      align-items: stretch;
    }

    .calendar-navigation {
      justify-content: center;
    }

    .view-selector {
      justify-content: center;
    }

    .today-btn {
      display: block;
      width: 100%;
    }

    .month-grid,
    .week-grid {
      height: auto;
    }

    .month-day {
      min-height: 60px;
    }

    .overview-stats {
      flex-direction: column;
    }

    .hot-job-item {
      flex-direction: column;
    }

    .hot-job-chart {
      width: 100%;
      margin-top: 1rem;
    }
  }
</style>

<!-- JavaScript Functionality -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    console.log("[FEED-DEBUG] DOM fully loaded and parsed");

    // Set current date
    const today = new Date();
    const options = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    document.getElementById("current-date").textContent =
      today.toLocaleDateString("en-US", options);
    console.log("[FEED-DEBUG] Current date set:", today.toLocaleDateString("en-US", options));

    // Define colors object for charts
    const colors = {
      primary: "#4f46e5",
      light: "rgba(79, 70, 229, 0.1)",
      secondary: "#0ea5e9",
      success: "#16a34a",
      warning: "#ca8a04",
      danger: "#dc2626",
    };

    // Initialize charts if elements exist
    if (document.getElementById("overview-chart")) {
      // Overview Chart
      const overviewChart = document
        .getElementById("overview-chart")
        .getContext("2d");
      new Chart(overviewChart, {
        type: "line",
        data: {
          labels: [
            {% for dates in overview_dates %}
            "{{ dates }}",
            {% endfor %}
           ],
          datasets: [
            {
              label: "New",
              data: {{ overview_new|safe }},
              borderColor: "#4f46e5",
              backgroundColor: "transparent",
              tension: 0.4,
              borderWidth: 2,
            },
            {
              label: "Rejected",
              data: {{ overview_reject|safe }},
              borderColor: "rgb(178, 8, 8)",
              backgroundColor: "transparent",
              tension: 0.4,
              borderWidth: 2,
            },
            {
              label: "In-Review",
              data: {{ overview_in_rev|safe }},
              borderColor: "#ca8a04",
              backgroundColor: "transparent",
              tension: 0.4,
              borderWidth: 2,
            },
            {
              label: "Final Stage",
              data: {{ overview_fin_stg|safe }},
              borderColor: "rgb(2, 14, 6)",
              backgroundColor: "transparent",
              tension: 0.4,
              borderWidth: 2,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
              align: "end",
              labels: {
                boxWidth: 12,
                padding: 15,
                usePointStyle: true,
                pointStyle: "circle",
              },
            },
          },
          scales: {
            x: {
              grid: {
                display: false,
              },
            },
            y: {
              beginAtZero: true,
              grid: {
                borderDash: [2, 4],
                drawBorder: false,
              },
              ticks: {
                precision: 0,
              },
            },
          },
        },
      });
    }

    // Initialize hot job charts if they exist
    {% for job in hot_jobs %}
    if (document.getElementById("job-chart-{{ forloop.counter }}")) {
      const jobChart = document
      .getElementById("job-chart-{{ forloop.counter }}")
      .getContext("2d");
      new Chart(jobChart, {
      type: "bar",
      data: {
        labels: {{ job.dates|safe }},
        datasets: [
        {
          label: "Applicants",
          data: {{ job.applicants|safe }},
          backgroundColor: colors.primary,
          borderColor: colors.primary,
          borderWidth: 1,
        },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
        legend: { display: false },
        tooltip: { enabled: true },
        },
        scales: {
        x: {
          grid: { display: false },
          ticks: { display: false }, // Make x-axis labels invisible
        },
        y: {
          beginAtZero: true,
          grid: { borderDash: [2, 4], drawBorder: false },
        },
        },
      },
      });
    }
    {% endfor %}

    // Only initialize if calendar elements exist
    if (document.getElementById("calendar-title")) {
      let calendarEvents = []; // Will be populated from the API
      let currentDate = new Date();
      let currentView = "month";
      let selectedDate = null;
      let selectedEvent = null; // Track the currently selected event for editing/deleting

      // DOM elements
      const monthViewEl = document.getElementById("month-view");
      const weekViewEl = document.getElementById("week-view");
      const dayViewEl = document.getElementById("day-view");
      const monthGridEl = document.getElementById("month-grid");
      const weekGridEl = document.getElementById("week-grid");
      const dayGridEl = document.getElementById("day-grid");
      const calendarTitleEl = document.getElementById("calendar-title");
      const monthViewBtn = document.getElementById("month-view-btn");
      const weekViewBtn = document.getElementById("week-view-btn");
      const dayViewBtn = document.getElementById("day-view-btn");
      const prevBtn = document.getElementById("prev-btn");
      const nextBtn = document.getElementById("next-btn");
      const todayBtn = document.getElementById("today-btn");

      // Modal elements
      const eventsModal = document.getElementById("events-modal");
      const modalTitle = document.getElementById("modal-title");
      const modalEvents = document.getElementById("modal-events");
      const closeModalBtns = document.querySelectorAll(".close-modal");

      // Event form elements
      const addEventBtn = document.getElementById("add-event-btn");
      const eventFormContainer = document.getElementById(
        "event-form-container"
      );
      const newEventForm = document.getElementById("new-event-form");
      const cancelEventBtn = document.getElementById("cancel-event-btn");
      const eventTitleInput = document.getElementById("event-title");
      const eventStartTimeInput = document.getElementById("event-start-time");
      const eventEndTimeInput = document.getElementById("event-end-time");
      const eventAttendeesInput = document.getElementById("event-attendees");
      const eventColorSelect = document.getElementById("event-color");

      // Fetch all appointments from the API
      function fetchAppointments() {
        console.log("[FEED-DEBUG] Fetching appointments from API");
        fetch("/api/appointments/")
          .then((response) => response.json())
          .then((data) => {
            // Transform API data to our calendar event format
            calendarEvents = data
              .filter(appointment => {
                // Filter out appointments with missing dates
                if (!appointment.start || !appointment.end) {
                  console.warn(`[FEED-DEBUG] Skipping appointment ${appointment.id} with missing dates`, appointment);
                  return false;
                }
                return true;
              })
              .map((appointment) => {
                // Log candidate name for debugging
                console.log(`[FEED-DEBUG] Processing appointment ${appointment.id} with candidate: ${appointment.candidate_name}`);
                console.log(`[FEED-DEBUG] Appointment data:`, appointment);

                return {
                  id: appointment.id,
                  title: appointment.title,
                  start: new Date(appointment.start),
                  end: new Date(appointment.end),
                  color: appointment.color || 1,
                  attendees: appointment.recruiters ? appointment.recruiters.split(',').map(r => r.trim()) : [],
                  vacancy: appointment.vacancy ? appointment.vacancy.vacancy_id : null,
                  candidate: appointment.candidate,
                  candidate_name: appointment.candidate_name, // Make sure to include this
                  event_type: appointment.event_type,
                  meeting_link: appointment.meeting_link,
                  inform_invitees: appointment.inform_invitees
                };
              });

            console.log("[FEED-DEBUG] Processed calendar events:", calendarEvents);
            // Update the calendar with the fetched events
            updateCalendar();


            // Remove loading text after calendar is initialized
            loadingText.remove();

          })
          .catch((error) =>
            console.error("Error fetching appointments:", error)
          );
      }

      // when generate-jitsi-link button is clicked, generate a random jitsi link and put it in the event-link-input
      const generateJitsiLinkBtn = document.getElementById("generate-jitsi-link");
      if (generateJitsiLinkBtn) {
        generateJitsiLinkBtn.addEventListener("click", function() {
          const eventLinkInput = document.getElementById("event-link");
          const randomString = Math.random().toString(36).substring(2, 25) + Math.random().toString(36).substring(2, 25);
          eventLinkInput.value = "https://p2p.mirotalk.com/join/" + randomString + "-canvider";
        });
      }

      // Add a new appointment
      function addAppointment(eventData) {
        console.log("[FEED-DEBUG] Adding appointment:", eventData);
        const data = {
          title: eventData.title,
          start_time: eventData.start.toISOString(),
          end_time: eventData.end.toISOString(),
          recruiters: eventData.recruiters || "",
          vacancy: eventData.vacancy || null,
          candidate: eventData.candidate || null,
          event_type: eventData.event_type || "",
          meeting_link: eventData.meeting_link || "",
          inform_invitees: eventData.inform_invitees || false,
          color: eventData.color || 1
        };

        return fetch("/api/add_appointment/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": getCsrfToken(),
          },
          body: JSON.stringify(data),
        })
          .then((response) => response.json())
          .then((result) => {
            if (result.success) {
              // Add ID to the event from the response
              eventData.id = result.id;
              calendarEvents.push(eventData);
              updateCalendar();
              return true;
            } else {
              console.error("Failed to add appointment:", result);
              return false;
            }
          })
          .catch((error) => {
            console.error("Error adding appointment:", error);
            return false;
          });
      }

      // Update an existing appointment
      function updateAppointment(eventData) {
        console.log("[FEED-DEBUG] Updating appointment:", eventData);
        const data = {
          title: eventData.title,
          start_time: eventData.start.toISOString(),
          end_time: eventData.end.toISOString(),
          recruiters: eventData.recruiters || "",
          vacancy: eventData.vacancy || null,
          candidate: eventData.candidate || null,
          event_type: eventData.event_type || "",
          meeting_link: eventData.meeting_link || "",
          inform_invitees: eventData.inform_invitees || false,
          color: eventData.color || 1
        };

        return fetch(`/api/update_appointment/${eventData.id}/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": getCsrfToken(),
          },
          body: JSON.stringify(data),
        })
          .then((response) => response.json())
          .then((result) => {
            if (result.success) {
              // Update the event in the local array
              const index = calendarEvents.findIndex(
                (event) => event.id === eventData.id
              );
              if (index !== -1) {
                calendarEvents[index] = eventData;
              }
              updateCalendar();
              return true;
            } else {
              console.error("Failed to update appointment:", result);
              return false;
            }
          })
          .catch((error) => {
            console.error("Error updating appointment:", error);
            return false;
          });
      }

      // Delete an appointment
      function deleteAppointment(eventId) {
        return fetch(`/api/delete_appointment/${eventId}/`, {
          method: "DELETE",
          headers: {
            "X-CSRFToken": getCsrfToken(),
          },
        })
          .then((response) => response.json())
          .then((result) => {
            if (result.success) {
              // Remove the event from the local array
              calendarEvents = calendarEvents.filter(
                (event) => event.id !== eventId
              );
              updateCalendar();
              return true;
            } else {
              console.error("Failed to delete appointment:", result);
              return false;
            }
          })
          .catch((error) => {
            console.error("Error deleting appointment:", error);
            return false;
          });
      }

      // Helper function to get CSRF token
      function getCsrfToken() {
        const cookieValue = document.cookie
          .split("; ")
          .find((row) => row.startsWith("csrftoken="))
          ?.split("=")[1];
        return cookieValue || "";
      }

      // Initialize calendar
      console.log("[FEED-DEBUG] Initializing calendar by fetching appointments");
      fetchAppointments();

      // Add loading text
      const loadingText = document.createElement('div');
      loadingText.textContent = 'Loading calendar...';
      loadingText.style.cssText = 'position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; color: #666;';
      document.querySelector('.calendar-container').appendChild(loadingText);

      console.log("[FEED-DEBUG] Calendar initialized");

      // Event listeners
      monthViewBtn.addEventListener("click", () => setView("month"));
      weekViewBtn.addEventListener("click", () => setView("week"));
      dayViewBtn.addEventListener("click", () => setView("day"));
      prevBtn.addEventListener("click", navigatePrev);
      nextBtn.addEventListener("click", navigateNext);
      todayBtn.addEventListener("click", navigateToday);

      // Modal close button
      closeModalBtns.forEach((btn) => {
        btn.addEventListener("click", function () {
          closeModal(eventsModal);
        });
      });

      // Close modal when clicking outside
      window.addEventListener("click", function (event) {
        if (event.target === eventsModal) {
          closeModal(eventsModal);
        }
      });

      // Add event button click handler
      if (addEventBtn) {
        addEventBtn.addEventListener("click", function () {
          // Reset selectedEvent since we're creating a new event
          selectedEvent = null;

          // Clear the form
          newEventForm.reset();

          // Show the event form
          eventFormContainer.style.display = "block";
          addEventBtn.style.display = "none";

          // Set default times (current time + 1 hour)
          const now = new Date();
          const startTime =
            now.getHours().toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");
          const endTime =
            (now.getHours() + 1).toString().padStart(2, "0") +
            ":" +
            now.getMinutes().toString().padStart(2, "0");

          eventStartTimeInput.value = startTime;
          eventEndTimeInput.value = endTime;
        });
      }

      // Cancel event button click handler
      if (cancelEventBtn) {
        cancelEventBtn.addEventListener("click", function () {
          // Hide the event form and show the add button
          eventFormContainer.style.display = "none";
          addEventBtn.style.display = "block";

          // Reset the form
          newEventForm.reset();
        });
      }

      // New event form submission
      if (newEventForm) {
        newEventForm.addEventListener("submit", function (e) {
          e.preventDefault();

          // Get form values
          const title = eventTitleInput.value;
          const startTime = eventStartTimeInput.value;
          const endTime = eventEndTimeInput.value;
          const eventType = document.getElementById("event-type").value;
          const recruiters = document.getElementById("recruiters-input").value;
          const vacancy = document.getElementById("vacancy").value;
          const candidate = document.getElementById("candidates-dropdown").value;
          const meetingLink = document.getElementById("event-link").value;
          const color = parseInt(eventColorSelect?.value || 1);
          const inform_invitees = document.getElementById("inform_invitees").checked;

          // Create start and end date objects
          const [startHour, startMinute] = startTime
            .split(":")
            .map((n) => parseInt(n));
          const [endHour, endMinute] = endTime
            .split(":")
            .map((n) => parseInt(n));

          const start = new Date(selectedDate);
          start.setHours(startHour, startMinute, 0);

          const end = new Date(selectedDate);
          end.setHours(endHour, endMinute, 0);

          // Validate end time is after start time
          if (end <= start) {
            alert("End time must be after start time");
            return;
          }

          // Show loading state
          const saveBtn = document.querySelector("#save-event-btn");
          const originalText = saveBtn.textContent;
          saveBtn.textContent = "Saving...";
          saveBtn.disabled = true;

          if (selectedEvent) {
            // Update existing event
            const updatedEvent = {
              id: selectedEvent.id,
              title,
              start,
              end,
              color,
              recruiters,
              vacancy,
              candidate,
              event_type: eventType,
              meeting_link: meetingLink,
              inform_invitees
            };

            updateAppointment(updatedEvent).then((success) => {
              if (success) {
                // Hide the form and show the add button
                eventFormContainer.style.display = "none";
                addEventBtn.style.display = "block";

                // Reset the form
                newEventForm.reset();

                // Refresh the modal to show the updated event
                showEventsModal(selectedDate, getEventsForDate(selectedDate));
              } else {
                alert("Failed to update the event. Please try again.");
              }

              // Reset button state
              saveBtn.textContent = originalText;
              saveBtn.disabled = false;
            });
          } else {
            // Create new event
            const newEvent = {
              title,
              start,
              end,
              color,
              recruiters,
              vacancy,
              candidate,
              event_type: eventType,
              meeting_link: meetingLink,
              inform_invitees
            };

            addAppointment(newEvent).then((success) => {
              if (success) {
                // Hide the form and show the add button
                eventFormContainer.style.display = "none";
                addEventBtn.style.display = "block";

                // Reset the form
                newEventForm.reset();

                // Refresh the modal to show the new event
                showEventsModal(selectedDate, getEventsForDate(selectedDate));
              } else {
                alert("Failed to save the event. Please try again.");
              }

              // Reset button state
              saveBtn.textContent = originalText;
              saveBtn.disabled = false;
            });
          }
        });
      }

      // Add delete event functionality
      function addDeleteButton(eventEl, event) {
        const deleteBtn = document.createElement("button");
        deleteBtn.className = "delete-event-btn";
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = "Delete event";

        deleteBtn.addEventListener("click", (e) => {
          e.stopPropagation(); // Prevent event modal from opening

          if (confirm("Are you sure you want to delete this event?")) {
            deleteAppointment(event.id).then((success) => {
              if (success) {
                // Close modal if open
                closeModal(eventsModal);
              } else {
                alert("Failed to delete the event. Please try again.");
              }
            });
          }
        });

        eventEl.appendChild(deleteBtn);
      }

      // Add edit event functionality
      function addEditButton(eventEl, event) {
        console.log("[FEED-DEBUG] Adding edit button for event:", event.id);
        const editBtn = document.createElement("button");
        editBtn.className = "edit-event-btn";
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = "Edit event";

        editBtn.addEventListener("click", (e) => {
          e.stopPropagation(); // Prevent event modal from opening

          // Set selectedEvent for the form
          selectedEvent = event;

          console.log("[FEED-DEBUG] Editing event:", event.id);
          console.log("[FEED-DEBUG] Event data:", event);

          // Populate form with event data
          eventTitleInput.value = event.title;

          // Format time for input
          eventStartTimeInput.value = `${event.start
            .getHours()
            .toString()
            .padStart(2, "0")}:${event.start
            .getMinutes()
            .toString()
            .padStart(2, "0")}`;
          eventEndTimeInput.value = `${event.end
            .getHours()
            .toString()
            .padStart(2, "0")}:${event.end
            .getMinutes()
            .toString()
            .padStart(2, "0")}`;

          // Handle attendees properly whether it's an array or string
          if (eventAttendeesInput) {
            if (Array.isArray(event.attendees)) {
              eventAttendeesInput.value = event.attendees.join(", ");
            } else if (typeof event.attendees === 'string') {
              eventAttendeesInput.value = event.attendees;
            } else {
              eventAttendeesInput.value = "";
            }
          }

          eventColorSelect.value = event.color;

          // Show form and hide add button
          eventFormContainer.style.display = "block";
          addEventBtn.style.display = "none";
        });

        eventEl.appendChild(editBtn);
      }

      // Set calendar view
      function setView(view) {
        currentView = view;
        console.log("[FEED-DEBUG] Setting view to:", view);

        // Update view buttons
        monthViewBtn.classList.toggle("active", view === "month");
        weekViewBtn.classList.toggle("active", view === "week");
        dayViewBtn.classList.toggle("active", view === "day");

        console.log("[FEED-DEBUG] View buttons updated");

        // Update view containers
        monthViewEl.classList.toggle("active", view === "month");
        weekViewEl.classList.toggle("active", view === "week");
        dayViewEl.classList.toggle("active", view === "day");

        // Show/hide month view info
        const monthViewInfo = document.getElementById("month-view-info");
        if (monthViewInfo) {
          monthViewInfo.style.display = view === "month" ? "block" : "none";
        }

        updateCalendar();
      }

      // Navigate to previous period
      function navigatePrev() {
        switch (currentView) {
          case "month":
            currentDate = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth() - 1,
              1
            );
            break;
          case "week":
            currentDate = new Date(
              currentDate.getTime() - 7 * 24 * 60 * 60 * 1000
            );
            break;
          case "day":
            currentDate = new Date(currentDate.getTime() - 24 * 60 * 60 * 1000);
            break;
        }
        updateCalendar();
      }

      // Navigate to next period
      function navigateNext() {
        switch (currentView) {
          case "month":
            currentDate = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth() + 1,
              1
            );
            break;
          case "week":
            currentDate = new Date(
              currentDate.getTime() + 7 * 24 * 60 * 60 * 1000
            );
            break;
          case "day":
            currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
            break;
        }
        updateCalendar();
      }

      // Navigate to today
      function navigateToday() {
        currentDate = new Date();
        updateCalendar();
      }

      // Update calendar based on current view and date
      function updateCalendar() {
        updateTitle();

        switch (currentView) {
          case "month":
            renderMonthView();
            break;
          case "week":
            renderWeekView();
            break;
          case "day":
            renderDayView();
            break;
        }
      }

      // Update calendar title
      function updateTitle() {
        const options = { month: "long", year: "numeric" };

        switch (currentView) {
          case "month":
            calendarTitleEl.textContent = currentDate.toLocaleDateString(
              "en-US",
              options
            );
            break;
          case "week":
            const weekStart = getStartOfWeek(currentDate);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);

            if (weekStart.getMonth() === weekEnd.getMonth()) {
              calendarTitleEl.textContent = `${weekStart.toLocaleDateString(
                "en-US",
                { month: "long", year: "numeric" }
              )} (${weekStart.getDate()}-${weekEnd.getDate()})`;
            } else {
              calendarTitleEl.textContent = `${weekStart.toLocaleDateString(
                "en-US",
                { month: "short", day: "numeric" }
              )} - ${weekEnd.toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}`;
            }
            break;
          case "day":
            calendarTitleEl.textContent = currentDate.toLocaleDateString(
              "en-US",
              {
                weekday: "long",
                month: "long",
                day: "numeric",
                year: "numeric",
              }
            );
            break;
        }
      }

      // Render month view
      function renderMonthView() {
        monthGridEl.innerHTML = "";

        const firstDay = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1
        );
        const lastDay = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0
        );

        // Get the day of week of the first day (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
        // Convert to Monday-first format
        let firstDayOfWeek = firstDay.getDay() || 7;
        firstDayOfWeek = firstDayOfWeek - 1; // Convert to 0-6 where 0 is Monday

        // Calculate days from previous month
        const prevMonthDays = firstDayOfWeek;
        const daysInPrevMonth = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          0
        ).getDate();

        // Days in current month
        const daysInMonth = lastDay.getDate();

        // Days from next month
        const nextMonthDays = 42 - (prevMonthDays + daysInMonth); // 42 = 6 rows * 7 days

        // Render previous month days
        for (let i = 0; i < prevMonthDays; i++) {
          const day = daysInPrevMonth - prevMonthDays + i + 1;
          const date = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - 1,
            day
          );
          const dayEl = createMonthDay(date, true);
          monthGridEl.appendChild(dayEl);
        }

        // Render current month days
        for (let i = 1; i <= daysInMonth; i++) {
          const date = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            i
          );
          const dayEl = createMonthDay(date, false);
          monthGridEl.appendChild(dayEl);
        }

        // Render next month days
        for (let i = 1; i <= nextMonthDays; i++) {
          const date = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 1,
            i
          );
          const dayEl = createMonthDay(date, true);
          monthGridEl.appendChild(dayEl);
        }
      }

      // Create a day element for month view
      function createMonthDay(date, inactive) {
        const dayEl = document.createElement("div");
        dayEl.className = "month-day";

        if (inactive) {
          dayEl.classList.add("inactive");
        }

        const today = new Date();
        if (
          date.getDate() === today.getDate() &&
          date.getMonth() === today.getMonth() &&
          date.getFullYear() === today.getFullYear()
        ) {
          dayEl.classList.add("today");
        }

        const dayNumber = document.createElement("div");
        dayNumber.className = "day-number";
        dayNumber.textContent = date.getDate();
        dayEl.appendChild(dayNumber);

        // Add event indicators for this day
        const dayEvents = getEventsForDate(date);

        if (dayEvents.length > 0) {
          const indicatorsContainer = document.createElement("div");
          indicatorsContainer.className = "event-indicators";

          // Create a Set to track unique colors we've seen
          const uniqueColors = new Set();
          dayEvents.forEach((event) => uniqueColors.add(event.color));

          // Create indicators for each unique color
          uniqueColors.forEach((color) => {
            const indicator = document.createElement("div");
            indicator.className = `event-indicator color-${color}`;
            indicatorsContainer.appendChild(indicator);
          });

          dayEl.appendChild(indicatorsContainer);
        }

        // Create a deep copy of the date to avoid reference issues
        const dateCopy = new Date(date.getTime());

        // Make the entire day clickable to show the modal
        dayEl.addEventListener("click", function (e) {
          e.stopPropagation(); // Prevent event bubbling
          selectedDate = dateCopy; // Store the selected date for event creation
          const clickedEvents = getEventsForDate(dateCopy);
          showEventsModal(dateCopy, clickedEvents);
        });

        return dayEl;
      }

      // Render week view
      function renderWeekView() {
        weekGridEl.innerHTML = "";

        const weekStart = getStartOfWeek(currentDate);

        for (let i = 0; i < 7; i++) {
          const date = new Date(weekStart);
          date.setDate(weekStart.getDate() + i);

          const dayEl = document.createElement("div");
          dayEl.className = "week-day";

          const today = new Date();
          if (
            date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear()
          ) {
            dayEl.classList.add("today");
          }

          const dayHeader = document.createElement("div");
          dayHeader.className = "day-header";
          dayHeader.textContent = date.toLocaleDateString("en-US", {
            weekday: "short",
            month: "numeric",
            day: "numeric",
          });
          dayEl.appendChild(dayHeader);

          // Add events for this day
          const dayEvents = getEventsForDate(date);

          // Sort events by start time
          dayEvents.sort((a, b) => a.start - b.start);

          dayEvents.forEach((event) => {
            const eventEl = document.createElement("div");
            eventEl.className = `event color-${event.color}`;

            const startTime = event.start.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            });
            eventEl.textContent = `${startTime} ${event.title}`;

            // Add edit and delete buttons
            console.log("[FEED-DEBUG] Adding edit button for event:", event.id);
            console.log("[FEED-DEBUG] Event data:", event);
            addEditButton(eventEl, event);
            addDeleteButton(eventEl, event);

            eventEl.addEventListener("click", (e) => {
              e.stopPropagation();
              selectedDate = new Date(date);
              showEventsModal(date, [event]);
            });

            dayEl.appendChild(eventEl);
          });

          // Make the entire day clickable to add events
          dayEl.addEventListener("click", function () {
            selectedDate = new Date(date);
            showEventsModal(date, getEventsForDate(date));
          });

          weekGridEl.appendChild(dayEl);
        }
      }

      // Render day view
      function renderDayView() {
        dayGridEl.innerHTML = "";

        for (let hour = 0; hour < 24; hour++) {
          const hourEl = document.createElement("div");
          hourEl.className = "day-hour";

          const hourLabel = document.createElement("div");
          hourLabel.className = "hour-label";
          hourLabel.textContent = hour.toString().padStart(2, "0") + ":00";
          hourEl.appendChild(hourLabel);

          const hourContent = document.createElement("div");
          hourContent.className = "hour-content";
          hourEl.appendChild(hourContent);

          // Add events for this hour
          const hourEvents = getEventsForHour(currentDate, hour);

          // Sort events by start time
          hourEvents.sort((a, b) => a.start - b.start);

          hourEvents.forEach((event) => {
            const eventEl = document.createElement("div");
            eventEl.className = `event color-${event.color}`;

            const startTime = event.start.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            });
            const endTime = event.end.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            });
            eventEl.textContent = `${startTime} - ${endTime} ${event.title}`;

            // Add edit and delete buttons
            addEditButton(eventEl, event);
            addDeleteButton(eventEl, event);

            eventEl.addEventListener("click", (e) => {
              e.stopPropagation();
              selectedDate = new Date(currentDate);
              showEventsModal(currentDate, [event]);
            });

            hourContent.appendChild(eventEl);
          });

          // Make hour clickable to add an event at that time
          hourEl.addEventListener("click", function () {
            selectedDate = new Date(currentDate);
            selectedDate.setHours(hour, 0, 0, 0);
            showEventsModal(selectedDate, []);

            // Set the time in the form
            if (eventStartTimeInput && eventEndTimeInput) {
              eventStartTimeInput.value = `${hour
                .toString()
                .padStart(2, "0")}:00`;
              eventEndTimeInput.value = `${(hour + 1)
                .toString()
                .padStart(2, "0")}:00`;
            }

            // Show the form
            if (addEventBtn && eventFormContainer) {
              addEventBtn.click();
            }
          });

          dayGridEl.appendChild(hourEl);
        }
      }

      // Get events for a specific date
      function getEventsForDate(date) {
        return calendarEvents.filter((event) => {
          return (
            event.start.getDate() === date.getDate() &&
            event.start.getMonth() === date.getMonth() &&
            event.start.getFullYear() === date.getFullYear()
          );
        });
      }

      // Get events for a specific hour
      function getEventsForHour(date, hour) {
        return calendarEvents.filter((event) => {
          return (
            event.start.getDate() === date.getDate() &&
            event.start.getMonth() === date.getMonth() &&
            event.start.getFullYear() === date.getFullYear() &&
            event.start.getHours() === hour
          );
        });
      }

      // Show modal with events for a specific date
      function showEventsModal(date, dayEvents) {
        // Store the selected date for event creation
        selectedDate = new Date(date);

        // Update modal title
        modalTitle.textContent = date.toLocaleDateString("en-US", {
          weekday: "long",
          month: "long",
          day: "numeric",
          year: "numeric",
        });

        // Clear previous events
        modalEvents.innerHTML = "";

        // Sort events by start time
        dayEvents.sort((a, b) => a.start - b.start);

        // Add events to container
        if (dayEvents.length === 0) {
          const noEvents = document.createElement("p");
          noEvents.textContent = "No events for this day.";
          modalEvents.appendChild(noEvents);
        } else {
          dayEvents.forEach((event) => {
            // Log event data to verify candidate_name is available
            console.log(`[FEED-DEBUG] Showing event in modal: ${event.id}, candidate: ${event.candidate_name}`);

            const eventEl = document.createElement("div");
            eventEl.className = `modal-event color-${event.color}`;

            const eventTime = document.createElement("div");
            eventTime.className = "modal-event-time";
            eventTime.textContent = `${event.start.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            })} - ${event.end.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            })}`;

            const eventTitle = document.createElement("div");
            eventTitle.className = "modal-event-title";
            eventTitle.textContent = event.title;

            const eventActions = document.createElement("div");
            eventActions.className = "modal-event-actions";

            const editBtn = document.createElement("button");
            editBtn.className = "edit-event-btn";
            editBtn.innerHTML = '<i class="fas fa-edit"></i>';
            editBtn.title = "Edit event";

            editBtn.addEventListener("click", () => {
              // Hide event list, show form
              eventFormContainer.style.display = "block";
              addEventBtn.style.display = "none";

              // Set form values
              selectedEvent = event;
              eventTitleInput.value = event.title;

              // Format time for input
              eventStartTimeInput.value = `${event.start
                .getHours()
                .toString()
                .padStart(2, "0")}:${event.start
                .getMinutes()
                .toString()
                .padStart(2, "0")}`;
              eventEndTimeInput.value = `${event.end
                .getHours()
                .toString()
                .padStart(2, "0")}:${event.end
                .getMinutes()
                .toString()
                .padStart(2, "0")}`;

              // Handle attendees properly whether it's an array or string
              if (eventAttendeesInput) {
                if (Array.isArray(event.attendees)) {
                  eventAttendeesInput.value = event.attendees.join(", ");
                } else if (typeof event.attendees === 'string') {
                  eventAttendeesInput.value = event.attendees;
                } else {
                  eventAttendeesInput.value = "";
                }
              }

              eventColorSelect.value = event.color;
            });

            const deleteBtn = document.createElement("button");
            deleteBtn.className = "delete-event-btn";
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = "Delete event";

            deleteBtn.addEventListener("click", () => {
              if (confirm("Are you sure you want to delete this event?")) {
                deleteAppointment(event.id).then((success) => {
                  if (success) {
                    closeModal(eventsModal);
                  } else {
                    alert("Failed to delete the event. Please try again.");
                  }
                });
              }
            });

            eventActions.appendChild(editBtn);
            eventActions.appendChild(deleteBtn);

            // Add attendees if any
            if (event.candidate && event.attendees && event.attendees.length > 0) {
              const attendeesList = document.createElement("div");
              attendeesList.className = "attendees-list";
              console.log("[FEED-DEBUG] event Data:", event);

              // Use candidate_name if available, otherwise fallback
              const candidateDisplay = event.candidate_name || `Candidate ID: ${event.candidate}`;
              attendeesList.textContent = `With: ${Array.isArray(event.attendees) ? event.attendees.join(", ") : event.attendees} and ${candidateDisplay} for the vacancy id: ${event.vacancy}.`;

              eventEl.appendChild(eventTime);
              eventEl.appendChild(eventTitle);
              eventEl.appendChild(attendeesList);
              eventEl.appendChild(eventActions);
            } else {
              eventEl.appendChild(eventTime);
              eventEl.appendChild(eventTitle);
              eventEl.appendChild(eventActions);
            }

            modalEvents.appendChild(eventEl);
          });
        }

        // Reset the event form
        if (eventFormContainer) {
          eventFormContainer.style.display = "none";
        }
        if (addEventBtn) {
          addEventBtn.style.display = "block";
        }
        if (newEventForm) {
          newEventForm.reset();
        }

        // Show the modal
        eventsModal.style.display = "block";
      }

      // Close modal helper function
      function closeModal(modal) {
        modal.style.display = "none";
      }

      // Get start of week (Monday)
      function getStartOfWeek(date) {
        const day = date.getDay() || 7; // Convert Sunday from 0 to 7
        const diff = date.getDate() - day + 1; // Adjust to Monday
        return new Date(date.setDate(diff));
      }
    }
  });
</script>
{% endblock content %}
